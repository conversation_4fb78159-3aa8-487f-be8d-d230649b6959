using System;
using System.ComponentModel.DataAnnotations;

namespace HotelMarketplace.Core.Models
{
    public class NotificationPreference
    {
        public int Id { get; set; }

        [Required]
        public string UserId { get; set; } = string.Empty;

        public bool EmailNotifications { get; set; } = true;
        public bool PushNotifications { get; set; } = true;
        public bool SmsNotifications { get; set; } = false;
        public bool BookingConfirmations { get; set; } = true;
        public bool BookingReminders { get; set; } = true;
        public bool PaymentNotifications { get; set; } = true;
        public bool PromotionalEmails { get; set; } = true;
        public bool NewsletterSubscription { get; set; } = false;
        public bool ReviewReminders { get; set; } = true;
        public bool PriceAlerts { get; set; } = false;
        public bool SystemUpdates { get; set; } = true;
        public bool SecurityAlerts { get; set; } = true;

        [MaxLength(10)]
        public string? PreferredLanguage { get; set; }

        [MaxLength(50)]
        public string? TimeZone { get; set; }

        public int? QuietHoursStart { get; set; } // Hour of day (0-23)
        public int? QuietHoursEnd { get; set; } // Hour of day (0-23)

        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        public User User { get; set; } = null!;
    }
}
