using HotelMarketplace.Core.Models;
using HotelMarketplace.Infrastructure.Data;
using Microsoft.AspNetCore.Identity;

namespace HotelMarketplace.WebAPI.Extensions
{
    public static class DatabaseSeeder
    {
        public static async Task SeedDataAsync(IServiceProvider serviceProvider)
        {
            using var scope = serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            var userManager = scope.ServiceProvider.GetRequiredService<UserManager<User>>();
            var roleManager = scope.ServiceProvider.GetRequiredService<RoleManager<IdentityRole>>();

            // Create roles if they don't exist
            await CreateRolesAsync(roleManager);

            // Create test users if they don't exist
            await CreateTestUsersAsync(userManager);

            // Create sample hotels and data
            await CreateSampleDataAsync(context, userManager);
        }

        private static async Task CreateRolesAsync(RoleManager<IdentityRole> roleManager)
        {
            string[] roles = { "Admin", "HotelOwner", "Customer" };

            foreach (var role in roles)
            {
                if (!await roleManager.RoleExistsAsync(role))
                {
                    await roleManager.CreateAsync(new IdentityRole(role));
                }
            }
        }

        private static async Task CreateTestUsersAsync(UserManager<User> userManager)
        {
            // Create Admin User
            var adminEmail = "<EMAIL>";
            if (await userManager.FindByEmailAsync(adminEmail) == null)
            {
                var adminUser = new User
                {
                    UserName = adminEmail,
                    Email = adminEmail,
                    FirstName = "Admin",
                    LastName = "User",
                    PhoneNumber = "+1234567890",
                    Role = UserRole.Admin,
                    CreatedAt = DateTime.UtcNow,
                    IsActive = true,
                    EmailConfirmed = true
                };

                var result = await userManager.CreateAsync(adminUser, "admin123");
                if (result.Succeeded)
                {
                    await userManager.AddToRoleAsync(adminUser, "Admin");
                }
            }

            // Create Customer User
            var customerEmail = "<EMAIL>";
            if (await userManager.FindByEmailAsync(customerEmail) == null)
            {
                var customerUser = new User
                {
                    UserName = customerEmail,
                    Email = customerEmail,
                    FirstName = "John",
                    LastName = "Doe",
                    PhoneNumber = "+1234567891",
                    Role = UserRole.Customer,
                    CreatedAt = DateTime.UtcNow,
                    IsActive = true,
                    EmailConfirmed = true
                };

                var result = await userManager.CreateAsync(customerUser, "user123");
                if (result.Succeeded)
                {
                    await userManager.AddToRoleAsync(customerUser, "Customer");
                }
            }

            // Create Hotel Owner User
            var ownerEmail = "<EMAIL>";
            if (await userManager.FindByEmailAsync(ownerEmail) == null)
            {
                var ownerUser = new User
                {
                    UserName = ownerEmail,
                    Email = ownerEmail,
                    FirstName = "Hotel",
                    LastName = "Owner",
                    PhoneNumber = "+1234567892",
                    Role = UserRole.HotelOwner,
                    CreatedAt = DateTime.UtcNow,
                    IsActive = true,
                    EmailConfirmed = true
                };

                var result = await userManager.CreateAsync(ownerUser, "owner123");
                if (result.Succeeded)
                {
                    await userManager.AddToRoleAsync(ownerUser, "HotelOwner");
                }
            }
        }

        private static async Task CreateSampleDataAsync(ApplicationDbContext context, UserManager<User> userManager)
        {
            // Check if we already have hotels
            if (context.Hotels.Any())
            {
                return; // Data already seeded
            }

            var hotelOwner = await userManager.FindByEmailAsync("<EMAIL>");
            if (hotelOwner == null) return;

            // Create sample hotels
            var hotels = new List<Hotel>
            {
                new Hotel
                {
                    OwnerId = hotelOwner.Id,
                    Name = "Grand Plaza Hotel",
                    Location = "New York, NY",
                    Description = "Luxury hotel in the heart of Manhattan with stunning city views and world-class amenities.",
                    Rating = 4.5f,
                    Status = HotelStatus.Active,
                    CreatedAt = DateTime.UtcNow
                },
                new Hotel
                {
                    OwnerId = hotelOwner.Id,
                    Name = "Ocean View Resort",
                    Location = "Miami, FL",
                    Description = "Beautiful beachfront resort with pristine ocean views and tropical paradise atmosphere.",
                    Rating = 4.3f,
                    Status = HotelStatus.Active,
                    CreatedAt = DateTime.UtcNow
                },
                new Hotel
                {
                    OwnerId = hotelOwner.Id,
                    Name = "Mountain Lodge",
                    Location = "Denver, CO",
                    Description = "Cozy mountain retreat perfect for outdoor enthusiasts and nature lovers.",
                    Rating = 4.7f,
                    Status = HotelStatus.Active,
                    CreatedAt = DateTime.UtcNow
                }
            };

            context.Hotels.AddRange(hotels);
            await context.SaveChangesAsync();

            // Create sample rooms for the first hotel
            var firstHotel = hotels.First();
            var rooms = new List<Room>
            {
                new Room
                {
                    HotelId = firstHotel.Id,
                    RoomType = RoomType.Double,
                    PricePerNight = 299m,
                    TotalRooms = 10,
                    Amenities = "WiFi,TV,Air Conditioning,Mini Bar",
                    CreatedAt = DateTime.UtcNow
                },
                new Room
                {
                    HotelId = firstHotel.Id,
                    RoomType = RoomType.Deluxe,
                    PricePerNight = 499m,
                    TotalRooms = 5,
                    Amenities = "WiFi,TV,Air Conditioning,Balcony,Mini Bar,Living Area",
                    CreatedAt = DateTime.UtcNow
                }
            };

            context.Rooms.AddRange(rooms);
            await context.SaveChangesAsync();
        }
    }
}
