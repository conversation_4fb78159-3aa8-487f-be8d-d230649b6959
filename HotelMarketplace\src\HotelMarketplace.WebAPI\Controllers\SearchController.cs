using HotelMarketplace.Application.Services;
using HotelMarketplace.Core.DTOs;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace HotelMarketplace.WebAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class SearchController : ControllerBase
    {
        private readonly ISearchService _searchService;
        private readonly IHotelService _hotelService;
        private readonly IRoomService _roomService;

        public SearchController(
            ISearchService searchService,
            IHotelService hotelService,
            IRoomService roomService)
        {
            _searchService = searchService;
            _hotelService = hotelService;
            _roomService = roomService;
        }

        [HttpGet("hotels")]
        public async Task<IActionResult> SearchHotels([FromQuery] HotelSearchDto searchDto)
        {
            var result = await _searchService.SearchHotelsAsync(searchDto);
            return Ok(result);
        }

        [HttpGet("hotels/advanced")]
        public async Task<IActionResult> AdvancedHotelSearch([FromQuery] AdvancedHotelSearchDto searchDto)
        {
            var result = await _searchService.AdvancedHotelSearchAsync(searchDto);
            return Ok(result);
        }

        [HttpGet("rooms")]
        public async Task<IActionResult> SearchRooms([FromQuery] RoomSearchDto searchDto)
        {
            var result = await _searchService.SearchRoomsAsync(searchDto);
            return Ok(result);
        }

        [HttpGet("suggestions")]
        public async Task<IActionResult> GetSearchSuggestions([FromQuery] string query, [FromQuery] int limit = 10)
        {
            var result = await _searchService.GetSearchSuggestionsAsync(query, limit);
            return Ok(result);
        }

        [HttpGet("popular-destinations")]
        public async Task<IActionResult> GetPopularDestinations([FromQuery] int limit = 10)
        {
            var result = await _searchService.GetPopularDestinationsAsync(limit);
            return Ok(result);
        }

        [HttpGet("filters")]
        public async Task<IActionResult> GetAvailableFilters([FromQuery] string? location = null)
        {
            var result = await _searchService.GetAvailableFiltersAsync(location);
            return Ok(result);
        }

        [HttpGet("nearby")]
        public async Task<IActionResult> GetNearbyHotels([FromQuery] double latitude, [FromQuery] double longitude, [FromQuery] double radiusKm = 10)
        {
            var result = await _searchService.GetNearbyHotelsAsync(latitude, longitude, radiusKm);
            return Ok(result);
        }

        [HttpPost("save-search")]
        public async Task<IActionResult> SaveSearch([FromBody] SaveSearchDto saveSearchDto)
        {
            var result = await _searchService.SaveSearchAsync(saveSearchDto);
            if (!result.Success)
                return BadRequest(result);

            return Ok(result);
        }

        [HttpGet("saved-searches")]
        public async Task<IActionResult> GetSavedSearches([FromQuery] string userId)
        {
            var result = await _searchService.GetSavedSearchesAsync(userId);
            return Ok(result);
        }

        [HttpDelete("saved-searches/{searchId}")]
        public async Task<IActionResult> DeleteSavedSearch(int searchId)
        {
            var result = await _searchService.DeleteSavedSearchAsync(searchId);
            if (!result.Success)
                return BadRequest(result);

            return Ok(result);
        }

        [HttpGet("trending")]
        public async Task<IActionResult> GetTrendingSearches([FromQuery] int limit = 10)
        {
            var result = await _searchService.GetTrendingSearchesAsync(limit);
            return Ok(result);
        }

        [HttpGet("autocomplete")]
        public async Task<IActionResult> GetAutocomplete([FromQuery] string query, [FromQuery] string type = "all")
        {
            var result = await _searchService.GetAutocompleteAsync(query, type);
            return Ok(result);
        }
    }
}
