<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Test - Hotel Marketplace</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ccc; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { margin: 10px 0; padding: 10px; background: #f8f9fa; border-left: 4px solid #007bff; }
        .success { border-left-color: #28a745; background: #d4edda; }
        .error { border-left-color: #dc3545; background: #f8d7da; }
        .info { border-left-color: #17a2b8; background: #d1ecf1; }
        .dashboard-link { display: block; margin: 10px 0; padding: 10px; background: #f8f9fa; border: 1px solid #ddd; text-decoration: none; color: #333; }
        .dashboard-link:hover { background: #e9ecef; }
    </style>
</head>
<body>
    <h1>🎯 Dashboard Access Test</h1>
    <p>Test role-based dashboard access for the Hotel Marketplace system</p>

    <div class="test-section">
        <h3>📊 Available Dashboards</h3>
        <a href="admin-dashboard.html" class="dashboard-link">
            <strong>🔧 Admin Dashboard</strong><br>
            <small>For system administrators - manage users, hotels, bookings, and system settings</small>
        </a>
        <a href="hotel-owner-dashboard.html" class="dashboard-link">
            <strong>🏨 Hotel Owner Dashboard</strong><br>
            <small>For hotel owners - manage hotels, rooms, bookings, and analytics</small>
        </a>
        <a href="customer-dashboard.html" class="dashboard-link">
            <strong>👤 Customer Dashboard</strong><br>
            <small>For customers - view bookings, reviews, favorites, and profile</small>
        </a>
    </div>

    <div class="test-section">
        <h3>🔐 Test Login & Auto-Redirect</h3>
        <p>Login with different user roles to test automatic dashboard redirection:</p>
        
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
            <div style="border: 1px solid #ddd; padding: 15px; border-radius: 5px;">
                <h4>👑 Admin User</h4>
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Password:</strong> admin123</p>
                <p><strong>Expected:</strong> Redirects to Admin Dashboard</p>
                <button onclick="testLogin('<EMAIL>', 'admin123', 'Admin')">Test Admin Login</button>
            </div>
            
            <div style="border: 1px solid #ddd; padding: 15px; border-radius: 5px;">
                <h4>🏨 Hotel Owner</h4>
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Password:</strong> owner123</p>
                <p><strong>Expected:</strong> Redirects to Hotel Owner Dashboard</p>
                <button onclick="testLogin('<EMAIL>', 'owner123', 'HotelOwner')">Test Owner Login</button>
            </div>
            
            <div style="border: 1px solid #ddd; padding: 15px; border-radius: 5px;">
                <h4>👤 Customer</h4>
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Password:</strong> user123</p>
                <p><strong>Expected:</strong> Redirects to Customer Dashboard</p>
                <button onclick="testLogin('<EMAIL>', 'user123', 'Customer')">Test Customer Login</button>
            </div>
        </div>
        
        <div id="login-result" class="result" style="display:none;"></div>
    </div>

    <div class="test-section">
        <h3>🔄 Current Login Status</h3>
        <div id="current-status">
            <p>Checking login status...</p>
        </div>
        <button onclick="checkCurrentStatus()">Refresh Status</button>
        <button onclick="logout()">Logout</button>
    </div>

    <div class="test-section">
        <h3>🚀 Quick Actions</h3>
        <button onclick="window.location.href='login.html'">Go to Login Page</button>
        <button onclick="window.location.href='register.html'">Go to Registration</button>
        <button onclick="window.location.href='index.html'">Go to Home</button>
        <button onclick="goToDashboard()">Go to My Dashboard</button>
    </div>

    <script>
        // Check current status on page load
        document.addEventListener('DOMContentLoaded', checkCurrentStatus);

        function checkCurrentStatus() {
            const token = localStorage.getItem('token');
            const user = JSON.parse(localStorage.getItem('user') || 'null');
            const statusDiv = document.getElementById('current-status');
            
            if (token && user) {
                statusDiv.innerHTML = `
                    <div class="success">
                        <p><strong>✅ Logged In</strong></p>
                        <p><strong>User:</strong> ${user.firstName || 'Unknown'} (${user.email})</p>
                        <p><strong>Role:</strong> ${getRoleDisplayName(user.role)}</p>
                        <p><strong>Dashboard:</strong> <a href="${getDashboardUrl(user.role)}">${getDashboardUrl(user.role)}</a></p>
                    </div>
                `;
            } else {
                statusDiv.innerHTML = `
                    <div class="error">
                        <p><strong>❌ Not Logged In</strong></p>
                        <p>Please login to access dashboards</p>
                    </div>
                `;
            }
        }

        async function testLogin(email, password, expectedRole) {
            const resultDiv = document.getElementById('login-result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '<div class="info">🔄 Testing login...</div>';
            
            try {
                const response = await fetch('http://localhost:5093/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email, password })
                });

                const result = await response.json();
                
                if (result.success) {
                    // Store token and decode user info
                    localStorage.setItem('token', result.data);
                    
                    let userRole = 'Customer';
                    let userInfo = { email, firstName: email.split('@')[0], role: userRole };
                    
                    try {
                        const tokenParts = result.data.split('.');
                        if (tokenParts.length === 3) {
                            const payload = JSON.parse(atob(tokenParts[1]));
                            userRole = payload.role || payload['http://schemas.microsoft.com/ws/2008/06/identity/claims/role'] || 'Customer';
                            userInfo.role = userRole;
                            userInfo.firstName = payload.given_name || payload.firstName || email.split('@')[0];
                            userInfo.lastName = payload.family_name || payload.lastName || '';
                        }
                    } catch (error) {
                        console.error('Error decoding token:', error);
                    }
                    
                    localStorage.setItem('user', JSON.stringify(userInfo));
                    
                    const dashboardUrl = getDashboardUrl(userRole);
                    
                    resultDiv.innerHTML = `
                        <div class="success">
                            <p><strong>✅ Login Successful!</strong></p>
                            <p><strong>Role:</strong> ${getRoleDisplayName(userRole)}</p>
                            <p><strong>Dashboard:</strong> <a href="${dashboardUrl}">${dashboardUrl}</a></p>
                            <p><strong>Auto-redirect in 3 seconds...</strong></p>
                        </div>
                    `;
                    
                    // Auto-redirect after 3 seconds
                    setTimeout(() => {
                        window.location.href = dashboardUrl;
                    }, 3000);
                    
                    // Update current status
                    checkCurrentStatus();
                    
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <p><strong>❌ Login Failed</strong></p>
                            <p>${result.message || 'Unknown error'}</p>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <p><strong>❌ Connection Error</strong></p>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        function logout() {
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            checkCurrentStatus();
            document.getElementById('login-result').style.display = 'none';
        }

        function goToDashboard() {
            const user = JSON.parse(localStorage.getItem('user') || 'null');
            if (user) {
                window.location.href = getDashboardUrl(user.role);
            } else {
                alert('Please login first!');
            }
        }

        function getDashboardUrl(role) {
            switch (role) {
                case 'Admin':
                case '0':
                    return 'admin-dashboard.html';
                case 'HotelOwner':
                case '1':
                    return 'hotel-owner-dashboard.html';
                case 'Customer':
                case '2':
                default:
                    return 'customer-dashboard.html';
            }
        }

        function getRoleDisplayName(role) {
            switch (role) {
                case 'Admin':
                case '0':
                    return 'Administrator';
                case 'HotelOwner':
                case '1':
                    return 'Hotel Owner';
                case 'Customer':
                case '2':
                default:
                    return 'Customer';
            }
        }
    </script>
</body>
</html>
