<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - Hotel Marketplace</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="css/output.css" rel="stylesheet">
</head>
<body class="bg-gray-50 font-sans">
    <!-- Alert Container -->
    <div id="alert-container" class="fixed top-4 right-4 z-50 w-80"></div>

    <!-- Header/Navigation (same as index.html) -->
    <header class="bg-white shadow-sm">
        <!-- Header content here (copy from index.html) -->
    </header>

    <!-- Register Section -->
    <section class="container mx-auto px-4 py-12">
        <div class="max-w-md mx-auto bg-white rounded-lg shadow-md overflow-hidden">
            <div
                class="px-6 py-8"
            >
                <h2 class="text-2xl font-bold text-center text-gray-800 mb-8">Create an Account</h2>

                <form id="register-form" class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="firstName" class="block text-gray-700 font-medium mb-2">First Name</label>
                            <input type="text" id="firstName" name="firstName" required class="input" placeholder="John">
                        </div>

                        <div>
                            <label for="lastName" class="block text-gray-700 font-medium mb-2">Last Name</label>
                            <input type="text" id="lastName" name="lastName" required class="input" placeholder="Doe">
                        </div>
                    </div>

                    <div>
                        <label for="email" class="block text-gray-700 font-medium mb-2">Email Address</label>
                        <input type="email" id="email" name="email" required class="input" placeholder="<EMAIL>">
                    </div>

                    <div>
                        <label for="phoneNumber" class="block text-gray-700 font-medium mb-2">Phone Number</label>
                        <input type="tel" id="phoneNumber" name="phoneNumber" required class="input" placeholder="+****************">
                    </div>

                    <div>
                        <label for="password" class="block text-gray-700 font-medium mb-2">Password</label>
                        <input type="password" id="password" name="password" required class="input" placeholder="••••••••">
                        <p class="mt-1 text-sm text-gray-500">Password must be at least 8 characters long and include uppercase, lowercase, numbers, and special characters.</p>
                    </div>

                    <div>
                        <label for="confirmPassword" class="block text-gray-700 font-medium mb-2">Confirm Password</label>
                        <input type="password" id="confirmPassword" name="confirmPassword" required class="input" placeholder="••••••••">
                    </div>

                    <div>
                        <label for="role" class="block text-gray-700 font-medium mb-2">Account Type</label>
                        <select id="role" name="role" required class="input">
                            <option value="2">Customer</option>
                            <option value="1">Hotel Owner</option>
                        </select>
                    </div>

                    <div class="flex items-center">
                        <input type="checkbox" id="terms" name="terms" required class="h-4 w-4 text-primary-600 border-gray-300 rounded">
                        <label for="terms" class="ml-2 block text-sm text-gray-700">
                            I agree to the <a href="#" class="text-primary-600 hover:text-primary-500">Terms and Conditions</a> and <a href="#" class="text-primary-600 hover:text-primary-500">Privacy Policy</a>
                        </label>
                    </div>

                    <div>
                        <button type="submit" class="w-full btn btn-primary py-3">Create Account</button>
                    </div>
                </form>

                <div class="mt-6 text-center">
                    <p class="text-sm text-gray-600">
                        Already have an account?
                        <a href="login.html" class="text-primary-600 hover:text-primary-500 font-medium">Sign in</a>
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between">
                <div class="mb-6 md:mb-0">
                    <h3 class="text-xl font-bold mb-2">Hotel Marketplace</h3>
                    <p class="text-gray-400">Find your perfect stay, anywhere in the world.</p>
                </div>

                <div class="grid grid-cols-2 md:grid-cols-3 gap-8">
                    <div>
                        <h4 class="text-lg font-semibold mb-3">Company</h4>
                        <ul class="space-y-2">
                            <li><a href="about.html" class="text-gray-400 hover:text-white">About Us</a></li>
                            <li><a href="#" class="text-gray-400 hover:text-white">Careers</a></li>
                            <li><a href="contact.html" class="text-gray-400 hover:text-white">Contact Us</a></li>
                        </ul>
                    </div>

                    <div>
                        <h4 class="text-lg font-semibold mb-3">Support</h4>
                        <ul class="space-y-2">
                            <li><a href="#" class="text-gray-400 hover:text-white">Help Center</a></li>
                            <li><a href="#" class="text-gray-400 hover:text-white">Safety Information</a></li>
                            <li><a href="#" class="text-gray-400 hover:text-white">Cancellation Options</a></li>
                        </ul>
                    </div>

                    <div>
                        <h4 class="text-lg font-semibold mb-3">Legal</h4>
                        <ul class="space-y-2">
                            <li><a href="#" class="text-gray-400 hover:text-white">Terms & Conditions</a></li>
                            <li><a href="#" class="text-gray-400 hover:text-white">Privacy Policy</a></li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="mt-8 pt-6 border-t border-gray-700 flex flex-col md:flex-row justify-between items-center">
                <p class="text-gray-400">&copy; 2023 Hotel Marketplace. All rights reserved.</p>

                <div class="mt-4 md:mt-0 flex space-x-4">
                    <a href="#" class="text-gray-400 hover:text-white">
                        <span class="sr-only">Facebook</span>
                        <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                            <path fill-rule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clip-rule="evenodd" />
                        </svg>
                    </a>

                    <a href="#" class="text-gray-400 hover:text-white">
                        <span class="sr-only">Twitter</span>
                        <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                            <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
                        </svg>
                    </a>

                    <a href="#" class="text-gray-400 hover:text-white">
                        <span class="sr-only">Instagram</span>
                        <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                            <path fill-rule="evenodd" d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z" clip-rule="evenodd" />
                        </svg>
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="js/api.js"></script>
    <script src="js/utils.js"></script>
    <script>


        document.addEventListener('DOMContentLoaded', async function() {
            // Clear any invalid tokens from previous sessions
            // Since we changed the backend, old tokens are invalid
            localStorage.removeItem('token');
            localStorage.removeItem('user');

            // Check if API server is running
            try {
                // Try to access the auth/register endpoint with OPTIONS method
                const response = await fetch('http://localhost:5093/api/auth/register', {
                    method: 'OPTIONS',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                // Even if we get an error response, as long as we get a response, the server is online
                utils.showAlert('API server is online!', 'success', 3000);
            } catch (error) {
                console.error('API server check failed:', error);
                utils.showAlert('Warning: API server appears to be offline. Registration may not work.', 'warning', 10000);
            }
            // Initialize mobile menu toggle
            const mobileMenuButton = document.getElementById('mobile-menu-button');
            const mobileMenu = document.getElementById('mobile-menu');

            if (mobileMenuButton && mobileMenu) {
                mobileMenuButton.addEventListener('click', function() {
                    mobileMenu.classList.toggle('hidden');
                });
            }

            // Note: Removed automatic redirect check since we cleared tokens above

            // Handle registration form submission
            const registerForm = document.getElementById('register-form');
            registerForm.addEventListener('submit', async function(e) {
                e.preventDefault();

                const password = document.getElementById('password').value;
                const confirmPassword = document.getElementById('confirmPassword').value;

                // Check if passwords match
                if (password !== confirmPassword) {
                    utils.showAlert('Passwords do not match.', 'error');
                    return;
                }

                // Prepare registration data
                const registrationData = {
                    firstName: document.getElementById('firstName').value,
                    lastName: document.getElementById('lastName').value,
                    email: document.getElementById('email').value,
                    phoneNumber: document.getElementById('phoneNumber').value,
                    password: password,
                    role: parseInt(document.getElementById('role').value)
                };

                try {
                    // Use direct fetch for better error handling
                    utils.showAlert('Attempting to register...', 'info');

                    // Make the API call directly
                    const response = await fetch('http://localhost:5093/api/auth/register', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(registrationData)
                    });

                    // Try to parse the response
                    let result;
                    try {
                        result = await response.json();
                    } catch (e) {
                        console.error('Error parsing response:', e);
                        throw new Error('Invalid response from server');
                    }

                    // Check if the request was successful
                    if (response.ok && result.success) {
                        // Store the token
                        localStorage.setItem('token', result.data);

                        // Show success message
                        utils.showAlert('Registration successful! Redirecting...', 'success');

                        // Redirect to home page
                        setTimeout(() => {
                            window.location.href = 'index.html';
                        }, 2000);
                    } else {
                        // Handle error response
                        console.error('Registration failed:', result);
                        utils.showAlert(result.message || 'Registration failed. Please try again.', 'error');
                    }
                } catch (error) {
                    console.error('Registration error:', error);

                    // More detailed error message
                    if (error.name === 'TypeError' && error.message === 'Failed to fetch') {
                        utils.showAlert('Unable to connect to the API server. Please ensure the backend server is running at http://localhost:5093', 'error');
                    } else if (error.message.includes('500')) {
                        utils.showAlert('Server error: The API server encountered an internal error. This might be due to database connection issues.', 'error');
                    } else {
                        utils.showAlert(`Error: ${error.message || 'An unknown error occurred'}. Please try again.`, 'error');
                    }
                }
            });
        });
    </script>
</body>
</html>


