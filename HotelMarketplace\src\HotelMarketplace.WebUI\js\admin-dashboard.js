// Admin dashboard functionality
class AdminDashboard {
    constructor() {
        this.currentSection = 'dashboard';
        this.charts = {};
        this.init();
    }

    init() {
        this.checkAdminAccess();
        this.setupEventListeners();
        this.loadDashboardData();
    }

    checkAdminAccess() {
        if (!utils.isLoggedIn()) {
            window.location.href = 'login.html';
            return;
        }

        const userRole = utils.getUserRole();
        if (userRole !== 'Admin') {
            utils.showAlert('Access denied. Admin privileges required.', 'error');
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 2000);
            return;
        }
    }

    setupEventListeners() {
        // Navigation links
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const section = link.dataset.section;
                this.showSection(section);
            });
        });

        // User search and filters
        const userSearch = document.getElementById('user-search');
        const userRoleFilter = document.getElementById('user-role-filter');
        
        if (userSearch) {
            userSearch.addEventListener('input', () => this.debounce(() => this.loadUsers(), 300)());
        }
        
        if (userRoleFilter) {
            userRoleFilter.addEventListener('change', () => this.loadUsers());
        }
    }

    showSection(sectionName) {
        // Hide all sections
        document.querySelectorAll('.admin-section').forEach(section => {
            section.classList.add('hidden');
        });

        // Show selected section
        const targetSection = document.getElementById(`${sectionName}-section`);
        if (targetSection) {
            targetSection.classList.remove('hidden');
        }

        // Update navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        
        const activeLink = document.querySelector(`[data-section="${sectionName}"]`);
        if (activeLink) {
            activeLink.classList.add('active');
        }

        this.currentSection = sectionName;

        // Load section-specific data
        switch (sectionName) {
            case 'dashboard':
                this.loadDashboardData();
                break;
            case 'users':
                this.loadUsers();
                break;
            case 'hotels':
                this.loadHotels();
                break;
            case 'bookings':
                this.loadBookings();
                break;
            case 'reviews':
                this.loadReviews();
                break;
            case 'reports':
                this.loadReports();
                break;
            case 'settings':
                this.loadSettings();
                break;
        }
    }

    async loadDashboardData() {
        try {
            // Mock data for demonstration - replace with actual API calls
            const mockData = {
                totalUsers: 1250,
                totalHotels: 89,
                totalBookings: 3420,
                totalRevenue: 125000,
                revenueData: [
                    { month: 'Jan', amount: 15000 },
                    { month: 'Feb', amount: 18000 },
                    { month: 'Mar', amount: 22000 },
                    { month: 'Apr', amount: 19000 },
                    { month: 'May', amount: 25000 },
                    { month: 'Jun', amount: 26000 }
                ],
                bookingData: [
                    { month: 'Jan', count: 450 },
                    { month: 'Feb', count: 520 },
                    { month: 'Mar', count: 680 },
                    { month: 'Apr', count: 590 },
                    { month: 'May', count: 720 },
                    { month: 'Jun', count: 760 }
                ],
                recentActivities: [
                    {
                        type: 'user_registered',
                        description: 'New user John Doe registered',
                        timestamp: new Date().toISOString()
                    },
                    {
                        type: 'hotel_created',
                        description: 'Grand Hotel submitted for approval',
                        timestamp: new Date(Date.now() - 3600000).toISOString()
                    },
                    {
                        type: 'booking_created',
                        description: 'New booking for Luxury Resort',
                        timestamp: new Date(Date.now() - 7200000).toISOString()
                    }
                ]
            };

            this.renderDashboardStats(mockData);
            this.renderCharts(mockData);
            this.renderRecentActivity(mockData.recentActivities);
        } catch (error) {
            console.error('Error loading dashboard data:', error);
            utils.showAlert('Error loading dashboard data', 'error');
        }
    }

    renderDashboardStats(data) {
        document.getElementById('total-users').textContent = data.totalUsers || 0;
        document.getElementById('total-hotels').textContent = data.totalHotels || 0;
        document.getElementById('total-bookings').textContent = data.totalBookings || 0;
        document.getElementById('total-revenue').textContent = `$${(data.totalRevenue || 0).toLocaleString()}`;
    }

    renderCharts(data) {
        this.renderRevenueChart(data.revenueData || []);
        this.renderBookingChart(data.bookingData || []);
    }

    renderRevenueChart(revenueData) {
        const ctx = document.getElementById('revenue-chart');
        if (!ctx) return;

        if (this.charts.revenue) {
            this.charts.revenue.destroy();
        }

        this.charts.revenue = new Chart(ctx, {
            type: 'line',
            data: {
                labels: revenueData.map(item => item.month || item.date),
                datasets: [{
                    label: 'Revenue',
                    data: revenueData.map(item => item.amount || 0),
                    borderColor: 'rgb(59, 130, 246)',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '$' + value.toLocaleString();
                            }
                        }
                    }
                }
            }
        });
    }

    renderBookingChart(bookingData) {
        const ctx = document.getElementById('booking-chart');
        if (!ctx) return;

        if (this.charts.booking) {
            this.charts.booking.destroy();
        }

        this.charts.booking = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: bookingData.map(item => item.month || item.date),
                datasets: [{
                    label: 'Bookings',
                    data: bookingData.map(item => item.count || 0),
                    backgroundColor: 'rgba(34, 197, 94, 0.8)',
                    borderColor: 'rgb(34, 197, 94)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    renderRecentActivity(activities) {
        const container = document.getElementById('recent-activity');
        if (!container) return;

        if (activities.length === 0) {
            container.innerHTML = '<p class="text-gray-600">No recent activity</p>';
            return;
        }

        const activitiesHTML = activities.map(activity => `
            <div class="flex items-center space-x-4 p-4 border rounded-lg">
                <div class="flex-shrink-0">
                    <i class="fas ${this.getActivityIcon(activity.type)} text-primary-600"></i>
                </div>
                <div class="flex-1">
                    <p class="text-sm font-medium text-gray-900">${activity.description}</p>
                    <p class="text-sm text-gray-600">${utils.formatDate(new Date(activity.timestamp))}</p>
                </div>
            </div>
        `).join('');

        container.innerHTML = activitiesHTML;
    }

    getActivityIcon(type) {
        const icons = {
            'user_registered': 'fa-user-plus',
            'hotel_created': 'fa-hotel',
            'booking_created': 'fa-calendar-plus',
            'review_posted': 'fa-star',
            'payment_received': 'fa-dollar-sign'
        };
        return icons[type] || 'fa-info-circle';
    }

    async loadUsers() {
        try {
            // Mock data for demonstration
            const mockUsers = [
                {
                    id: '1',
                    firstName: 'John',
                    lastName: 'Doe',
                    email: '<EMAIL>',
                    role: 'Customer',
                    isActive: true,
                    createdAt: new Date().toISOString()
                },
                {
                    id: '2',
                    firstName: 'Jane',
                    lastName: 'Smith',
                    email: '<EMAIL>',
                    role: 'HotelOwner',
                    isActive: true,
                    createdAt: new Date().toISOString()
                },
                {
                    id: '3',
                    firstName: 'Admin',
                    lastName: 'User',
                    email: '<EMAIL>',
                    role: 'Admin',
                    isActive: true,
                    createdAt: new Date().toISOString()
                }
            ];

            this.renderUsersTable(mockUsers);
        } catch (error) {
            console.error('Error loading users:', error);
            utils.showAlert('Error loading users', 'error');
        }
    }

    renderUsersTable(users) {
        const tbody = document.getElementById('users-table-body');
        if (!tbody) return;

        if (users.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="5" class="px-6 py-4 text-center text-gray-500">No users found</td>
                </tr>
            `;
            return;
        }

        const usersHTML = users.map(user => `
            <tr>
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 h-10 w-10">
                            <div class="h-10 w-10 rounded-full bg-primary-100 flex items-center justify-center">
                                <span class="text-primary-600 font-medium">${user.firstName?.[0] || 'U'}</span>
                            </div>
                        </div>
                        <div class="ml-4">
                            <div class="text-sm font-medium text-gray-900">${user.firstName} ${user.lastName}</div>
                            <div class="text-sm text-gray-500">${user.email}</div>
                        </div>
                    </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${this.getRoleBadgeClass(user.role)}">
                        ${user.role}
                    </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${user.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                        ${user.isActive ? 'Active' : 'Inactive'}
                    </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    ${utils.formatDate(new Date(user.createdAt))}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button onclick="adminDashboard.viewUser('${user.id}')" class="text-primary-600 hover:text-primary-900 mr-3">View</button>
                    <button onclick="adminDashboard.toggleUserStatus('${user.id}', ${!user.isActive})" class="text-${user.isActive ? 'red' : 'green'}-600 hover:text-${user.isActive ? 'red' : 'green'}-900">
                        ${user.isActive ? 'Deactivate' : 'Activate'}
                    </button>
                </td>
            </tr>
        `).join('');

        tbody.innerHTML = usersHTML;
    }

    getRoleBadgeClass(role) {
        const classes = {
            'Admin': 'bg-purple-100 text-purple-800',
            'HotelOwner': 'bg-blue-100 text-blue-800',
            'Customer': 'bg-gray-100 text-gray-800'
        };
        return classes[role] || 'bg-gray-100 text-gray-800';
    }

    // Placeholder methods for other functionality
    async loadHotels() {
        console.log('Loading hotels...');
    }

    async loadBookings() {
        console.log('Loading bookings...');
    }

    async loadReviews() {
        console.log('Loading reviews...');
    }

    async loadReports() {
        console.log('Loading reports...');
    }

    async loadSettings() {
        console.log('Loading settings...');
    }

    // User actions
    async viewUser(userId) {
        console.log('View user:', userId);
    }

    async toggleUserStatus(userId, newStatus) {
        console.log('Toggle user status:', userId, newStatus);
        utils.showAlert(`User ${newStatus ? 'activated' : 'deactivated'} successfully`, 'success');
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// Initialize admin dashboard when DOM is loaded
let adminDashboard;
document.addEventListener('DOMContentLoaded', () => {
    adminDashboard = new AdminDashboard();
});
