using System;
using System.ComponentModel.DataAnnotations;

namespace HotelMarketplace.Core.Models
{
    public class SavedSearch
    {
        public int Id { get; set; }

        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;

        [Required]
        [MaxLength(500)]
        public string SearchQuery { get; set; } = string.Empty;

        [MaxLength(100)]
        public string? Location { get; set; }

        public DateTime? CheckIn { get; set; }

        public DateTime? CheckOut { get; set; }

        public int Guests { get; set; } = 1;

        public int Rooms { get; set; } = 1;

        public string? Filters { get; set; } // JSON string of applied filters

        public bool EnableAlerts { get; set; }

        public DateTime CreatedAt { get; set; }

        public DateTime? LastUsed { get; set; }

        public int UseCount { get; set; }

        // Foreign Keys
        [Required]
        public string UserId { get; set; } = string.Empty;

        // Navigation Properties
        public User User { get; set; } = null!;
    }
}
