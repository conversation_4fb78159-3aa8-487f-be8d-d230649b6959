using HotelMarketplace.Core.DTOs;
using System;
using System.ComponentModel.DataAnnotations;

namespace HotelMarketplace.Core.Models
{
    public class NotificationTemplate
    {
        public int Id { get; set; }

        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;

        [Required]
        [MaxLength(200)]
        public string Subject { get; set; } = string.Empty;

        [Required]
        public string Body { get; set; } = string.Empty;

        public string? SmsTemplate { get; set; }

        public string? PushTemplate { get; set; }

        public NotificationType Type { get; set; }

        public bool IsActive { get; set; } = true;

        [MaxLength(10)]
        public string? Language { get; set; } = "en";

        public string? Variables { get; set; } // JSON string of available variables

        public DateTime CreatedAt { get; set; }

        public DateTime? UpdatedAt { get; set; }

        public string? CreatedByUserId { get; set; }

        public string? UpdatedByUserId { get; set; }

        // Navigation Properties
        public User? CreatedByUser { get; set; }
        public User? UpdatedByUser { get; set; }
    }
}
