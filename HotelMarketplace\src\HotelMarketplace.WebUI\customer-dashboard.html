<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Dashboard - Hotel Marketplace</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="css/output.css" rel="stylesheet">
    <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
</head>
<body class="bg-gray-50 font-sans">
    <!-- Alert Container -->
    <div id="alert-container" class="fixed top-4 right-4 z-50 w-80"></div>

    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="container mx-auto px-4 py-3">
            <div class="flex justify-between items-center">
                <div class="flex items-center">
                    <a href="index.html" class="text-2xl font-bold text-primary-600 mr-8">HotelMarketplace</a>
                    <span class="text-gray-600">My Dashboard</span>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="hotels.html" class="text-gray-600 hover:text-gray-900">Browse Hotels</a>
                    <div class="relative group">
                        <button class="flex items-center space-x-2 text-gray-700 hover:text-gray-900">
                            <i class="fas fa-user-circle text-xl"></i>
                            <span id="user-name">Customer</span>
                            <i class="fas fa-chevron-down text-sm"></i>
                        </button>
                        <div class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10 hidden group-hover:block">
                            <a href="#profile" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Profile</a>
                            <a href="#settings" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Settings</a>
                            <hr class="my-1">
                            <a href="#" onclick="utils.logout()" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Logout</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <div class="flex">
        <!-- Sidebar -->
        <aside class="w-64 bg-white shadow-sm min-h-screen">
            <nav class="p-4">
                <ul class="space-y-2">
                    <li>
                        <a href="#dashboard" class="nav-link active" data-section="dashboard">
                            <i class="fas fa-tachometer-alt mr-3"></i>
                            Dashboard
                        </a>
                    </li>
                    <li>
                        <a href="#bookings" class="nav-link" data-section="bookings">
                            <i class="fas fa-calendar-check mr-3"></i>
                            My Bookings
                        </a>
                    </li>
                    <li>
                        <a href="#reviews" class="nav-link" data-section="reviews">
                            <i class="fas fa-star mr-3"></i>
                            My Reviews
                        </a>
                    </li>
                    <li>
                        <a href="#favorites" class="nav-link" data-section="favorites">
                            <i class="fas fa-heart mr-3"></i>
                            Favorites
                        </a>
                    </li>
                    <li>
                        <a href="#profile" class="nav-link" data-section="profile">
                            <i class="fas fa-user mr-3"></i>
                            Profile
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="flex-1 p-6">
            <!-- Dashboard Section -->
            <section id="dashboard-section" class="dashboard-section">
                <div class="mb-6">
                    <h1 class="text-2xl font-bold text-gray-900">Welcome Back!</h1>
                    <p class="text-gray-600">Here's an overview of your bookings and activity.</p>
                </div>

                <!-- Quick Stats -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                                <i class="fas fa-calendar-check text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">Total Bookings</p>
                                <p id="total-bookings" class="text-2xl font-bold text-gray-900">0</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-green-100 text-green-600">
                                <i class="fas fa-star text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">Reviews Written</p>
                                <p id="total-reviews" class="text-2xl font-bold text-gray-900">0</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                                <i class="fas fa-heart text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">Favorite Hotels</p>
                                <p id="total-favorites" class="text-2xl font-bold text-gray-900">0</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Bookings -->
                <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold">Recent Bookings</h3>
                        <a href="#bookings" class="text-primary-600 hover:text-primary-700">View All</a>
                    </div>
                    <div id="recent-bookings" class="space-y-4">
                        <p class="text-gray-500 text-center py-8">No bookings yet. <a href="hotels.html" class="text-primary-600 hover:text-primary-700">Browse hotels</a> to make your first booking!</p>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <h3 class="text-lg font-semibold mb-4">Quick Actions</h3>
                        <div class="space-y-3">
                            <a href="hotels.html" class="flex items-center p-3 rounded-lg border hover:bg-gray-50">
                                <i class="fas fa-search text-primary-600 mr-3"></i>
                                <span>Search Hotels</span>
                            </a>
                            <a href="#bookings" class="flex items-center p-3 rounded-lg border hover:bg-gray-50">
                                <i class="fas fa-calendar text-primary-600 mr-3"></i>
                                <span>View My Bookings</span>
                            </a>
                            <a href="#reviews" class="flex items-center p-3 rounded-lg border hover:bg-gray-50">
                                <i class="fas fa-star text-primary-600 mr-3"></i>
                                <span>Write a Review</span>
                            </a>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <h3 class="text-lg font-semibold mb-4">Recommended for You</h3>
                        <div id="recommended-hotels" class="space-y-3">
                            <p class="text-gray-500">Discover amazing hotels based on your preferences!</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Bookings Section -->
            <section id="bookings-section" class="dashboard-section hidden">
                <div class="mb-6">
                    <h1 class="text-2xl font-bold text-gray-900">My Bookings</h1>
                    <p class="text-gray-600">Manage your hotel reservations.</p>
                </div>

                <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                    <div class="p-6 border-b">
                        <div class="flex justify-between items-center">
                            <h3 class="text-lg font-semibold">All Bookings</h3>
                            <select id="booking-filter" class="input">
                                <option value="">All Bookings</option>
                                <option value="Pending">Pending</option>
                                <option value="Confirmed">Confirmed</option>
                                <option value="Completed">Completed</option>
                                <option value="Cancelled">Cancelled</option>
                            </select>
                        </div>
                    </div>
                    <div id="bookings-list" class="p-6">
                        <p class="text-gray-500 text-center py-8">No bookings found. <a href="hotels.html" class="text-primary-600 hover:text-primary-700">Start exploring hotels!</a></p>
                    </div>
                </div>
            </section>

            <!-- Reviews Section -->
            <section id="reviews-section" class="dashboard-section hidden">
                <div class="mb-6">
                    <h1 class="text-2xl font-bold text-gray-900">My Reviews</h1>
                    <p class="text-gray-600">Reviews you've written for hotels.</p>
                </div>

                <div class="bg-white rounded-lg shadow-sm p-6">
                    <div id="reviews-list">
                        <p class="text-gray-500 text-center py-8">No reviews yet. Complete a booking to write your first review!</p>
                    </div>
                </div>
            </section>

            <!-- Favorites Section -->
            <section id="favorites-section" class="dashboard-section hidden">
                <div class="mb-6">
                    <h1 class="text-2xl font-bold text-gray-900">Favorite Hotels</h1>
                    <p class="text-gray-600">Hotels you've saved for later.</p>
                </div>

                <div class="bg-white rounded-lg shadow-sm p-6">
                    <div id="favorites-list">
                        <p class="text-gray-500 text-center py-8">No favorite hotels yet. <a href="hotels.html" class="text-primary-600 hover:text-primary-700">Browse hotels</a> and save your favorites!</p>
                    </div>
                </div>
            </section>

            <!-- Profile Section -->
            <section id="profile-section" class="dashboard-section hidden">
                <div class="mb-6">
                    <h1 class="text-2xl font-bold text-gray-900">My Profile</h1>
                    <p class="text-gray-600">Manage your account information.</p>
                </div>

                <div class="bg-white rounded-lg shadow-sm p-6">
                    <form id="profile-form">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="firstName" class="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                                <input type="text" id="firstName" name="firstName" class="input" required>
                            </div>
                            <div>
                                <label for="lastName" class="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                                <input type="text" id="lastName" name="lastName" class="input" required>
                            </div>
                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                                <input type="email" id="email" name="email" class="input" required readonly>
                            </div>
                            <div>
                                <label for="phoneNumber" class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                                <input type="tel" id="phoneNumber" name="phoneNumber" class="input">
                            </div>
                        </div>
                        <div class="mt-6">
                            <button type="submit" class="btn btn-primary">Update Profile</button>
                        </div>
                    </form>
                </div>
            </section>
        </main>
    </div>

    <!-- Scripts -->
    <script src="js/api.js"></script>
    <script src="js/utils.js"></script>
    <script>
        // Customer Dashboard JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            // Check authentication
            if (!utils.isLoggedIn()) {
                window.location.href = 'login.html';
                return;
            }

            // Load user info
            const user = utils.getCurrentUser();
            if (user && user.firstName) {
                document.getElementById('user-name').textContent = user.firstName;
            }

            // Navigation handling
            const navLinks = document.querySelectorAll('.nav-link');
            const sections = document.querySelectorAll('.dashboard-section');

            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // Remove active class from all links
                    navLinks.forEach(l => l.classList.remove('active'));
                    // Add active class to clicked link
                    this.classList.add('active');
                    
                    // Hide all sections
                    sections.forEach(s => s.classList.add('hidden'));
                    // Show target section
                    const targetSection = this.getAttribute('data-section') + '-section';
                    document.getElementById(targetSection).classList.remove('hidden');
                });
            });

            // Load dashboard data
            loadDashboardData();
        });

        async function loadDashboardData() {
            try {
                // Load user's bookings, reviews, etc.
                // This would connect to the real API
                console.log('Loading customer dashboard data...');
                
                // For now, show placeholder data
                document.getElementById('total-bookings').textContent = '0';
                document.getElementById('total-reviews').textContent = '0';
                document.getElementById('total-favorites').textContent = '0';
                
            } catch (error) {
                console.error('Error loading dashboard data:', error);
                utils.showAlert('Error loading dashboard data', 'error');
            }
        }
    </script>

    <style>
        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            color: #6b7280;
            text-decoration: none;
            border-radius: 0.5rem;
            transition: all 0.15s ease-in-out;
        }

        .nav-link:hover {
            background-color: #f3f4f6;
            color: #374151;
        }

        .nav-link.active {
            background-color: #3b82f6;
            color: white;
        }
    </style>
</body>
</html>
