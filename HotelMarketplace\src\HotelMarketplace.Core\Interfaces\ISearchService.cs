using HotelMarketplace.Core.DTOs;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace HotelMarketplace.Core.Interfaces
{
    public interface ISearchService
    {
        // Basic Search
        Task<ApiResponse<PagedResult<HotelDto>>> SearchHotelsAsync(HotelSearchDto searchDto);
        Task<ApiResponse<PagedResult<HotelDto>>> AdvancedHotelSearchAsync(AdvancedHotelSearchDto searchDto);
        Task<ApiResponse<PagedResult<RoomDto>>> SearchRoomsAsync(RoomSearchDto searchDto);

        // Search Suggestions and Autocomplete
        Task<ApiResponse<IEnumerable<SearchSuggestionDto>>> GetSearchSuggestionsAsync(string query, int limit);
        Task<ApiResponse<IEnumerable<AutocompleteDto>>> GetAutocompleteAsync(string query, string type);
        Task<ApiResponse<IEnumerable<DestinationDto>>> GetPopularDestinationsAsync(int limit);
        Task<ApiResponse<IEnumerable<TrendingSearchDto>>> GetTrendingSearchesAsync(int limit);

        // Location-based Search
        Task<ApiResponse<IEnumerable<HotelDto>>> GetNearbyHotelsAsync(double latitude, double longitude, double radiusKm);

        // Filters and Facets
        Task<ApiResponse<SearchFiltersDto>> GetAvailableFiltersAsync(string? location);

        // Saved Searches
        Task<ApiResponse<bool>> SaveSearchAsync(SaveSearchDto saveSearchDto);
        Task<ApiResponse<IEnumerable<SavedSearchDto>>> GetSavedSearchesAsync(string userId);
        Task<ApiResponse<bool>> DeleteSavedSearchAsync(int searchId);

        // Search Analytics
        Task<ApiResponse<bool>> TrackSearchAsync(SearchTrackingDto trackingDto);
        Task<ApiResponse<SearchAnalyticsDto>> GetSearchAnalyticsAsync(int days);
    }
}
