# PowerShell script to check SQLite database content
Write-Host "Checking SQLite Database Content..." -ForegroundColor Green

# Check if sqlite3 is available
try {
    $sqliteVersion = sqlite3 -version
    Write-Host "SQLite Version: $sqliteVersion" -ForegroundColor Yellow
} catch {
    Write-Host "SQLite3 not found. Please install SQLite3 to check database content." -ForegroundColor Red
    exit 1
}

# Database path
$dbPath = "src\HotelMarketplace.WebAPI\hotelmarketplace.db"

if (Test-Path $dbPath) {
    Write-Host "Database found at: $dbPath" -ForegroundColor Green
    
    Write-Host "`n=== USERS TABLE ===" -ForegroundColor Cyan
    sqlite3 $dbPath "SELECT Id, Email, FirstName, LastName, Role, IsActive FROM AspNetUsers;"
    
    Write-Host "`n=== ROLES TABLE ===" -ForegroundColor Cyan
    sqlite3 $dbPath "SELECT * FROM AspNetRoles;"
    
    Write-Host "`n=== USER ROLES TABLE ===" -ForegroundColor Cyan
    sqlite3 $dbPath "SELECT * FROM AspNetUserRoles;"
    
    Write-Host "`n=== HOTELS TABLE ===" -ForegroundColor Cyan
    sqlite3 $dbPath "SELECT Id, Name, Location, OwnerId FROM Hotels;"
    
    Write-Host "`n=== ROOMS TABLE ===" -ForegroundColor Cyan
    sqlite3 $dbPath "SELECT Id, HotelId, RoomType, PricePerNight FROM Rooms;"
    
} else {
    Write-Host "Database not found at: $dbPath" -ForegroundColor Red
}

Write-Host "`nDatabase check complete!" -ForegroundColor Green
