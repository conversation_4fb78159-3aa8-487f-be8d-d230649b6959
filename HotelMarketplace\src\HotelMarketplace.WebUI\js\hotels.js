// Hotel listing and search functionality
class HotelManager {
    constructor() {
        this.currentPage = 1;
        this.pageSize = 12;
        this.currentFilters = {};
        this.currentSort = 'price-asc';
        this.viewMode = 'grid';
        this.hotels = [];
        this.totalCount = 0;
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadFiltersFromURL();
        this.loadHotels();
    }

    setupEventListeners() {
        // Search form
        const searchForm = document.getElementById('search-form');
        if (searchForm) {
            searchForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleSearch();
            });
        }

        // Price range slider
        const priceRange = document.getElementById('price-range');
        if (priceRange) {
            priceRange.addEventListener('input', (e) => {
                document.getElementById('price-value').textContent = `$${e.target.value}`;
                this.debounce(() => this.applyFilters(), 500)();
            });
        }

        // Filter checkboxes
        document.querySelectorAll('.star-filter, .amenity-filter').forEach(checkbox => {
            checkbox.addEventListener('change', () => this.applyFilters());
        });

        // Room type filter
        const roomTypeFilter = document.getElementById('room-type-filter');
        if (roomTypeFilter) {
            roomTypeFilter.addEventListener('change', () => this.applyFilters());
        }

        // Sort dropdown
        const sortBy = document.getElementById('sort-by');
        if (sortBy) {
            sortBy.addEventListener('change', (e) => {
                this.currentSort = e.target.value;
                this.sortHotels();
            });
        }

        // View mode buttons
        const gridView = document.getElementById('grid-view');
        const listView = document.getElementById('list-view');
        
        if (gridView && listView) {
            gridView.addEventListener('click', () => this.setViewMode('grid'));
            listView.addEventListener('click', () => this.setViewMode('list'));
        }

        // Clear filters
        const clearFilters = document.getElementById('clear-filters');
        if (clearFilters) {
            clearFilters.addEventListener('click', () => this.clearAllFilters());
        }
    }

    loadFiltersFromURL() {
        const urlParams = new URLSearchParams(window.location.search);
        
        // Load search parameters
        const location = urlParams.get('location');
        const checkIn = urlParams.get('checkIn');
        const checkOut = urlParams.get('checkOut');
        const guests = urlParams.get('guests');

        if (location) document.getElementById('location').value = location;
        if (checkIn) document.getElementById('check-in').value = checkIn;
        if (checkOut) document.getElementById('check-out').value = checkOut;
        if (guests) document.getElementById('guests').value = guests;

        // Load filter parameters
        const maxPrice = urlParams.get('maxPrice');
        const stars = urlParams.getAll('stars');
        const amenities = urlParams.getAll('amenities');
        const roomType = urlParams.get('roomType');

        if (maxPrice) {
            document.getElementById('price-range').value = maxPrice;
            document.getElementById('price-value').textContent = `$${maxPrice}`;
        }

        stars.forEach(star => {
            const checkbox = document.querySelector(`.star-filter[value="${star}"]`);
            if (checkbox) checkbox.checked = true;
        });

        amenities.forEach(amenity => {
            const checkbox = document.querySelector(`.amenity-filter[value="${amenity}"]`);
            if (checkbox) checkbox.checked = true;
        });

        if (roomType) {
            document.getElementById('room-type-filter').value = roomType;
        }
    }

    handleSearch() {
        this.currentPage = 1;
        this.updateURL();
        this.loadHotels();
    }

    applyFilters() {
        this.currentPage = 1;
        this.updateURL();
        this.loadHotels();
    }

    updateURL() {
        const params = new URLSearchParams();
        
        // Search parameters
        const location = document.getElementById('location').value;
        const checkIn = document.getElementById('check-in').value;
        const checkOut = document.getElementById('check-out').value;
        const guests = document.getElementById('guests').value;

        if (location) params.set('location', location);
        if (checkIn) params.set('checkIn', checkIn);
        if (checkOut) params.set('checkOut', checkOut);
        if (guests) params.set('guests', guests);

        // Filter parameters
        const maxPrice = document.getElementById('price-range').value;
        if (maxPrice && maxPrice !== '500') params.set('maxPrice', maxPrice);

        const selectedStars = Array.from(document.querySelectorAll('.star-filter:checked'))
            .map(cb => cb.value);
        selectedStars.forEach(star => params.append('stars', star));

        const selectedAmenities = Array.from(document.querySelectorAll('.amenity-filter:checked'))
            .map(cb => cb.value);
        selectedAmenities.forEach(amenity => params.append('amenities', amenity));

        const roomType = document.getElementById('room-type-filter').value;
        if (roomType) params.set('roomType', roomType);

        // Update URL without page reload
        const newURL = `${window.location.pathname}?${params.toString()}`;
        window.history.replaceState({}, '', newURL);
    }

    async loadHotels() {
        try {
            this.showLoading();
            
            const searchParams = this.buildSearchParams();
            const response = await window.api.hotels.searchHotels(searchParams);
            
            if (response.success) {
                this.hotels = response.data.items || response.data;
                this.totalCount = response.data.totalCount || this.hotels.length;
                this.renderHotels();
                this.renderPagination();
                this.updateResultsCount();
            } else {
                this.showError('Failed to load hotels');
            }
        } catch (error) {
            console.error('Error loading hotels:', error);
            this.showError('Error loading hotels. Please try again.');
        }
    }

    buildSearchParams() {
        const params = {
            page: this.currentPage,
            pageSize: this.pageSize,
            sortBy: this.currentSort
        };

        // Add search criteria
        const location = document.getElementById('location').value;
        const checkIn = document.getElementById('check-in').value;
        const checkOut = document.getElementById('check-out').value;
        const guests = document.getElementById('guests').value;

        if (location) params.location = location;
        if (checkIn) params.checkIn = checkIn;
        if (checkOut) params.checkOut = checkOut;
        if (guests) params.guests = guests;

        // Add filters
        const maxPrice = document.getElementById('price-range').value;
        if (maxPrice && maxPrice !== '500') params.maxPrice = maxPrice;

        const selectedStars = Array.from(document.querySelectorAll('.star-filter:checked'))
            .map(cb => cb.value);
        if (selectedStars.length > 0) params.stars = selectedStars;

        const selectedAmenities = Array.from(document.querySelectorAll('.amenity-filter:checked'))
            .map(cb => cb.value);
        if (selectedAmenities.length > 0) params.amenities = selectedAmenities;

        const roomType = document.getElementById('room-type-filter').value;
        if (roomType) params.roomType = roomType;

        return params;
    }

    renderHotels() {
        const container = document.getElementById('hotels-container');
        if (!container) return;

        if (this.hotels.length === 0) {
            container.innerHTML = `
                <div class="text-center py-12">
                    <i class="fas fa-search text-4xl text-gray-400 mb-4"></i>
                    <h3 class="text-xl font-medium text-gray-900 mb-2">No hotels found</h3>
                    <p class="text-gray-600">Try adjusting your search criteria or filters</p>
                </div>
            `;
            return;
        }

        const hotelsHTML = this.hotels.map(hotel => this.renderHotelCard(hotel)).join('');
        
        if (this.viewMode === 'grid') {
            container.innerHTML = `<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">${hotelsHTML}</div>`;
        } else {
            container.innerHTML = `<div class="space-y-6">${hotelsHTML}</div>`;
        }
    }

    renderHotelCard(hotel) {
        const isListView = this.viewMode === 'list';
        
        return `
            <div class="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow ${isListView ? 'flex' : ''}">
                <img src="${hotel.imageUrl || 'images/hotel-placeholder.jpg'}" 
                     alt="${hotel.name}" 
                     class="${isListView ? 'w-64 h-48' : 'w-full h-48'} object-cover ${isListView ? 'rounded-l-lg' : 'rounded-t-lg'}">
                
                <div class="p-6 ${isListView ? 'flex-1' : ''}">
                    <div class="flex justify-between items-start mb-2">
                        <h3 class="text-xl font-semibold text-gray-900">${hotel.name}</h3>
                        <div class="flex items-center bg-yellow-100 text-yellow-800 px-2 py-1 rounded">
                            <i class="fas fa-star text-sm mr-1"></i>
                            <span class="text-sm font-medium">${hotel.rating?.toFixed(1) || 'N/A'}</span>
                        </div>
                    </div>
                    
                    <p class="text-gray-600 mb-2">
                        <i class="fas fa-map-marker-alt mr-1"></i>
                        ${hotel.location}
                    </p>
                    
                    <p class="text-gray-700 mb-4">${hotel.description || 'Comfortable accommodation with modern amenities.'}</p>
                    
                    <div class="flex justify-between items-center">
                        <div>
                            <span class="text-2xl font-bold text-primary-600">$${hotel.lowestPrice?.toFixed(2) || '0.00'}</span>
                            <span class="text-gray-600">/night</span>
                        </div>
                        <a href="hotel-details.html?id=${hotel.id}" class="btn btn-primary">
                            View Details
                        </a>
                    </div>
                </div>
            </div>
        `;
    }

    setViewMode(mode) {
        this.viewMode = mode;
        
        const gridBtn = document.getElementById('grid-view');
        const listBtn = document.getElementById('list-view');
        
        if (mode === 'grid') {
            gridBtn.classList.add('bg-primary-600', 'text-white');
            gridBtn.classList.remove('bg-gray-200', 'text-gray-600');
            listBtn.classList.add('bg-gray-200', 'text-gray-600');
            listBtn.classList.remove('bg-primary-600', 'text-white');
        } else {
            listBtn.classList.add('bg-primary-600', 'text-white');
            listBtn.classList.remove('bg-gray-200', 'text-gray-600');
            gridBtn.classList.add('bg-gray-200', 'text-gray-600');
            gridBtn.classList.remove('bg-primary-600', 'text-white');
        }
        
        this.renderHotels();
    }

    sortHotels() {
        // This would typically be handled by the API, but we can also sort client-side
        this.loadHotels();
    }

    clearAllFilters() {
        // Reset all filter inputs
        document.getElementById('price-range').value = '500';
        document.getElementById('price-value').textContent = '$500';
        
        document.querySelectorAll('.star-filter, .amenity-filter').forEach(cb => {
            cb.checked = false;
        });
        
        document.getElementById('room-type-filter').value = '';
        
        this.applyFilters();
    }

    renderPagination() {
        const pagination = document.getElementById('pagination');
        if (!pagination) return;

        const totalPages = Math.ceil(this.totalCount / this.pageSize);
        
        if (totalPages <= 1) {
            pagination.innerHTML = '';
            return;
        }

        let paginationHTML = '';
        
        // Previous button
        if (this.currentPage > 1) {
            paginationHTML += `
                <button onclick="hotelManager.goToPage(${this.currentPage - 1})" 
                        class="px-3 py-2 text-gray-500 hover:text-gray-700">
                    <i class="fas fa-chevron-left"></i>
                </button>
            `;
        }

        // Page numbers
        for (let i = 1; i <= totalPages; i++) {
            if (i === this.currentPage) {
                paginationHTML += `
                    <button class="px-4 py-2 bg-primary-600 text-white rounded">${i}</button>
                `;
            } else {
                paginationHTML += `
                    <button onclick="hotelManager.goToPage(${i})" 
                            class="px-4 py-2 text-gray-700 hover:bg-gray-100 rounded">${i}</button>
                `;
            }
        }

        // Next button
        if (this.currentPage < totalPages) {
            paginationHTML += `
                <button onclick="hotelManager.goToPage(${this.currentPage + 1})" 
                        class="px-3 py-2 text-gray-500 hover:text-gray-700">
                    <i class="fas fa-chevron-right"></i>
                </button>
            `;
        }

        pagination.innerHTML = `<div class="flex items-center space-x-2">${paginationHTML}</div>`;
    }

    goToPage(page) {
        this.currentPage = page;
        this.loadHotels();
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }

    updateResultsCount() {
        const resultsCount = document.getElementById('results-count');
        if (resultsCount) {
            const start = (this.currentPage - 1) * this.pageSize + 1;
            const end = Math.min(this.currentPage * this.pageSize, this.totalCount);
            resultsCount.textContent = `Showing ${start}-${end} of ${this.totalCount} hotels`;
        }
    }

    showLoading() {
        const container = document.getElementById('hotels-container');
        if (container) {
            container.innerHTML = `
                <div class="flex justify-center items-center py-12">
                    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
                </div>
            `;
        }
    }

    showError(message) {
        const container = document.getElementById('hotels-container');
        if (container) {
            container.innerHTML = `
                <div class="text-center py-12">
                    <i class="fas fa-exclamation-triangle text-4xl text-red-400 mb-4"></i>
                    <h3 class="text-xl font-medium text-gray-900 mb-2">Error</h3>
                    <p class="text-gray-600">${message}</p>
                    <button onclick="hotelManager.loadHotels()" class="btn btn-primary mt-4">
                        Try Again
                    </button>
                </div>
            `;
        }
        
        if (window.utils) {
            window.utils.showAlert(message, 'error');
        }
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// Initialize hotel manager when DOM is loaded
let hotelManager;
document.addEventListener('DOMContentLoaded', () => {
    hotelManager = new HotelManager();
});
