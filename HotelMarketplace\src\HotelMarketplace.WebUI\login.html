<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Hotel Marketplace</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="css/output.css" rel="stylesheet">
</head>
<body class="bg-gray-50 font-sans">
    <!-- Alert Container -->
    <div id="alert-container" class="fixed top-4 right-4 z-50 w-80"></div>

    <!-- Header/Navigation -->
    <header class="bg-white shadow-sm">
        <div class="container mx-auto px-4 py-3">
            <nav class="flex items-center justify-between">
                <a href="index.html" class="flex items-center">
                    <span class="text-2xl font-bold text-primary-600">HotelMarketplace</span>
                </a>

                <div class="hidden md:flex space-x-6">
                    <a href="index.html" class="text-gray-800 hover:text-primary-600 font-medium">Home</a>
                    <a href="hotels.html" class="text-gray-800 hover:text-primary-600 font-medium">Hotels</a>
                    <a href="about.html" class="text-gray-800 hover:text-primary-600 font-medium">About</a>
                    <a href="contact.html" class="text-gray-800 hover:text-primary-600 font-medium">Contact</a>
                </div>

                <div class="flex items-center space-x-4">
                    <a href="login.html" class="btn btn-outline">Login</a>
                    <a href="register.html" class="btn btn-primary">Sign Up</a>
                </div>

                <!-- Mobile menu button -->
                <button id="mobile-menu-button" class="md:hidden">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                </button>
            </nav>

            <!-- Mobile menu -->
            <div id="mobile-menu" class="hidden md:hidden mt-4 pb-4">
                <a href="index.html" class="block py-2 text-gray-800 hover:text-primary-600">Home</a>
                <a href="hotels.html" class="block py-2 text-gray-800 hover:text-primary-600">Hotels</a>
                <a href="about.html" class="block py-2 text-gray-800 hover:text-primary-600">About</a>
                <a href="contact.html" class="block py-2 text-gray-800 hover:text-primary-600">Contact</a>
            </div>
        </div>
    </header>

    <!-- Login Section -->
    <section class="container mx-auto px-4 py-12">
        <div class="max-w-md mx-auto bg-white rounded-lg shadow-md overflow-hidden">
            <div class="px-6 py-8">
                <h2 class="text-2xl font-bold text-center mb-6">Login to Your Account</h2>

                <form id="login-form">
                    <div class="mb-4">
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
                        <input type="email" id="email" name="email" class="input" required>
                    </div>

                    <div class="mb-6">
                        <label for="password" class="block text-sm font-medium text-gray-700 mb-1">Password</label>
                        <input type="password" id="password" name="password" class="input" required>
                    </div>

                    <button type="submit" class="btn btn-primary w-full mb-4">Login</button>

                    <p class="text-center text-gray-600">
                        Don't have an account? <a href="register.html" class="text-primary-600 hover:underline">Register</a>
                    </p>
                </form>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8 mt-12">
        <div class="container mx-auto px-4 text-center">
            <p>&copy; 2024 Hotel Marketplace. All rights reserved.</p>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="js/api.js"></script>
    <script src="js/utils.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Clear any invalid tokens from previous sessions
            // Since we changed the backend, old tokens are invalid
            localStorage.removeItem('token');
            localStorage.removeItem('user');

            // Initialize mobile menu toggle
            const mobileMenuButton = document.getElementById('mobile-menu-button');
            const mobileMenu = document.getElementById('mobile-menu');

            if (mobileMenuButton && mobileMenu) {
                mobileMenuButton.addEventListener('click', function() {
                    mobileMenu.classList.toggle('hidden');
                });
            }

            // Update navigation based on authentication status
            utils.updateNavigation();

            // Handle login form submission
            const loginForm = document.getElementById('login-form');

            if (loginForm) {
                loginForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const formData = new FormData(loginForm);
                    const credentials = {
                        email: formData.get('email'),
                        password: formData.get('password')
                    };

                    // Validate email
                    if (!utils.validateEmail(credentials.email)) {
                        utils.showAlert('Please enter a valid email address', 'error');
                        return;
                    }

                    // Call login API directly with fetch for better error handling
                    utils.showAlert('Attempting to login...', 'info');

                    fetch('http://localhost:5093/api/auth/login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(credentials)
                    })
                    .then(response => response.json())
                    .then(result => {
                        if (result.success) {
                            // Store token (the API returns the token in result.data)
                            localStorage.setItem('token', result.data);

                            // Decode JWT token to get user role
                            let userRole = 'Customer'; // default
                            let userInfo = {
                                email: credentials.email,
                                firstName: credentials.email.split('@')[0],
                                role: userRole
                            };

                            try {
                                // Decode JWT token to extract user information
                                const tokenParts = result.data.split('.');
                                if (tokenParts.length === 3) {
                                    const payload = JSON.parse(atob(tokenParts[1]));
                                    userRole = payload.role || payload['http://schemas.microsoft.com/ws/2008/06/identity/claims/role'] || 'Customer';
                                    userInfo.role = userRole;
                                    userInfo.firstName = payload.given_name || payload.firstName || credentials.email.split('@')[0];
                                    userInfo.lastName = payload.family_name || payload.lastName || '';
                                    userInfo.userId = payload.sub || payload.nameid || '';
                                }
                            } catch (error) {
                                console.error('Error decoding token:', error);
                            }

                            localStorage.setItem('user', JSON.stringify(userInfo));

                            utils.showAlert('Login successful! Redirecting...', 'success');

                            // Redirect based on user role
                            setTimeout(() => {
                                let redirectUrl;

                                // Check if there's a specific redirect URL
                                const urlRedirect = new URLSearchParams(window.location.search).get('redirect');
                                if (urlRedirect) {
                                    redirectUrl = urlRedirect;
                                } else {
                                    // Redirect based on role
                                    switch (userRole) {
                                        case 'Admin':
                                        case '0': // Admin enum value
                                            redirectUrl = 'admin-dashboard.html';
                                            break;
                                        case 'HotelOwner':
                                        case '1': // HotelOwner enum value
                                            redirectUrl = 'hotel-owner-dashboard.html';
                                            break;
                                        case 'Customer':
                                        case '2': // Customer enum value
                                        default:
                                            redirectUrl = 'customer-dashboard.html';
                                            break;
                                    }
                                }

                                window.location.href = redirectUrl;
                            }, 1500);
                        } else {
                            utils.showAlert(result.message || 'Login failed. Please try again.', 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Login error:', error);
                        utils.showAlert('Unable to connect to the server. Please try again.', 'error');
                    });
                });
            }
        });
    </script>
</body>
</html>

