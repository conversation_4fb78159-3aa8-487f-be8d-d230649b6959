using System;
using System.ComponentModel.DataAnnotations;

namespace HotelMarketplace.Core.Models
{
    public class SystemLog
    {
        public int Id { get; set; }

        [Required]
        [MaxLength(50)]
        public string Level { get; set; } = string.Empty; // Info, Warning, Error, Debug

        [Required]
        [MaxLength(2000)]
        public string Message { get; set; } = string.Empty;

        public string? Exception { get; set; }

        public DateTime Timestamp { get; set; }

        public string? UserId { get; set; }

        [MaxLength(100)]
        public string? Action { get; set; }

        [MaxLength(100)]
        public string? Controller { get; set; }

        [MaxLength(500)]
        public string? RequestPath { get; set; }

        [MaxLength(10)]
        public string? HttpMethod { get; set; }

        [MaxLength(45)]
        public string? IpAddress { get; set; }

        [MaxLength(500)]
        public string? UserAgent { get; set; }

        public int? StatusCode { get; set; }

        public long? ResponseTime { get; set; } // in milliseconds

        // Navigation Properties
        public User? User { get; set; }
    }

    public enum LogLevel
    {
        Debug,
        Info,
        Warning,
        Error,
        Critical
    }
}
