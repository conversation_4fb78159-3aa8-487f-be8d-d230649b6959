using HotelMarketplace.Core.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace HotelMarketplace.Core.DTOs
{
    public class RoomDto
    {
        public int Id { get; set; }
        public int HotelId { get; set; }
        public string HotelName { get; set; }
        public string HotelCity { get; set; }
        public RoomType RoomType { get; set; }
        public string RoomTypeName { get; set; }
        public string RoomNumber { get; set; }
        public decimal PricePerNight { get; set; }
        public decimal? DiscountedPrice { get; set; }
        public int TotalRooms { get; set; }
        public int AvailableRooms { get; set; }
        public int MaxOccupancy { get; set; }
        public int BedCount { get; set; }
        public string BedType { get; set; }
        public double RoomSize { get; set; }
        public string RoomSizeUnit { get; set; }
        public string Description { get; set; }
        public List<string> Amenities { get; set; }
        public List<string> ImageUrls { get; set; }
        public bool IsAvailable { get; set; }
        public bool HasBalcony { get; set; }
        public bool HasKitchen { get; set; }
        public bool HasAirConditioning { get; set; }
        public bool HasWifi { get; set; }
        public string ViewType { get; set; }
        public string FloorNumber { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    public class RoomCreateDto
    {
        [Required]
        public int HotelId { get; set; }

        [Required]
        public RoomType RoomType { get; set; }

        [StringLength(50)]
        public string RoomNumber { get; set; }

        [Required]
        [Range(0.01, 10000)]
        public decimal PricePerNight { get; set; }

        [Range(0.01, 10000)]
        public decimal? DiscountedPrice { get; set; }

        [Required]
        [Range(1, 1000)]
        public int TotalRooms { get; set; }

        [Required]
        [Range(1, 20)]
        public int MaxOccupancy { get; set; }

        [Required]
        [Range(1, 10)]
        public int BedCount { get; set; }

        [StringLength(50)]
        public string BedType { get; set; } = "Queen";

        [Range(1, 1000)]
        public double RoomSize { get; set; }

        [StringLength(10)]
        public string RoomSizeUnit { get; set; } = "sqm";

        [StringLength(1000)]
        public string Description { get; set; }

        public List<string> Amenities { get; set; } = new List<string>();
        public List<string> ImageUrls { get; set; } = new List<string>();

        public bool HasBalcony { get; set; }
        public bool HasKitchen { get; set; }
        public bool HasAirConditioning { get; set; } = true;
        public bool HasWifi { get; set; } = true;

        [StringLength(50)]
        public string ViewType { get; set; }

        [StringLength(10)]
        public string FloorNumber { get; set; }
    }

    public class RoomUpdateDto
    {
        [Required]
        public RoomType RoomType { get; set; }

        [StringLength(50)]
        public string RoomNumber { get; set; }

        [Required]
        [Range(0.01, 10000)]
        public decimal PricePerNight { get; set; }

        [Range(0.01, 10000)]
        public decimal? DiscountedPrice { get; set; }

        [Required]
        [Range(1, 1000)]
        public int TotalRooms { get; set; }

        [Required]
        [Range(1, 20)]
        public int MaxOccupancy { get; set; }

        [Required]
        [Range(1, 10)]
        public int BedCount { get; set; }

        [StringLength(50)]
        public string BedType { get; set; }

        [Range(1, 1000)]
        public double RoomSize { get; set; }

        [StringLength(10)]
        public string RoomSizeUnit { get; set; }

        [StringLength(1000)]
        public string Description { get; set; }

        public List<string> Amenities { get; set; }
        public List<string> ImageUrls { get; set; }

        public bool IsAvailable { get; set; }
        public bool HasBalcony { get; set; }
        public bool HasKitchen { get; set; }
        public bool HasAirConditioning { get; set; }
        public bool HasWifi { get; set; }

        [StringLength(50)]
        public string ViewType { get; set; }

        [StringLength(10)]
        public string FloorNumber { get; set; }
    }

    public class RoomAvailabilityDto
    {
        public int RoomId { get; set; }
        public DateTime CheckIn { get; set; }
        public DateTime CheckOut { get; set; }
        public int AvailableCount { get; set; }
        public decimal TotalPrice { get; set; }
        public int NumberOfNights { get; set; }
    }

    public class RoomDetailDto : RoomDto
    {
        public HotelDto Hotel { get; set; }
        public List<ReviewDto> Reviews { get; set; }
        public List<BookingDto> RecentBookings { get; set; }
        public bool IsBookable { get; set; }
        public DateTime? NextAvailableDate { get; set; }
    }

    public class RoomSearchDto
    {
        public string? City { get; set; }
        public DateTime? CheckIn { get; set; }
        public DateTime? CheckOut { get; set; }
        public int? Guests { get; set; }
        public RoomType? RoomType { get; set; }
        public decimal? MinPrice { get; set; }
        public decimal? MaxPrice { get; set; }
        public List<string>? Amenities { get; set; }
        public bool? HasWifi { get; set; }
        public bool? HasAirConditioning { get; set; }
        public bool? HasBalcony { get; set; }
        public bool? HasKitchen { get; set; }
        public string? ViewType { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 10;
        public string? SortBy { get; set; } = "price";
        public string? SortOrder { get; set; } = "asc";
    }

    public class UpdateRoomAvailabilityDto
    {
        [Required]
        public DateTime StartDate { get; set; }

        [Required]
        public DateTime EndDate { get; set; }

        [Required]
        [Range(0, 1000)]
        public int AvailableCount { get; set; }

        public string? Reason { get; set; }
    }

    public class RoomPricingDto
    {
        public int RoomId { get; set; }
        public DateTime Date { get; set; }
        public decimal BasePrice { get; set; }
        public decimal? DiscountedPrice { get; set; }
        public string? DiscountReason { get; set; }
        public bool IsAvailable { get; set; }
        public int AvailableCount { get; set; }
    }
}
