using System;
using System.ComponentModel.DataAnnotations;

namespace HotelMarketplace.Core.Models
{
    public class SystemSetting
    {
        public int Id { get; set; }

        [Required]
        [MaxLength(100)]
        public string Key { get; set; } = string.Empty;

        [Required]
        public string Value { get; set; } = string.Empty;

        [MaxLength(500)]
        public string? Description { get; set; }

        [MaxLength(50)]
        public string Category { get; set; } = string.Empty; // General, Email, Payment, etc.

        [MaxLength(50)]
        public string DataType { get; set; } = "string"; // string, int, bool, decimal, json

        public bool IsPublic { get; set; } // Can be accessed by non-admin users

        public DateTime CreatedAt { get; set; }

        public DateTime? UpdatedAt { get; set; }

        public string? UpdatedByUserId { get; set; }

        // Navigation Properties
        public User? UpdatedByUser { get; set; }
    }

    public static class SystemSettingKeys
    {
        // General Settings
        public const string SiteName = "site_name";
        public const string SiteDescription = "site_description";
        public const string ContactEmail = "contact_email";
        public const string SupportPhone = "support_phone";
        public const string MaintenanceMode = "maintenance_mode";
        public const string MaintenanceMessage = "maintenance_message";

        // Business Settings
        public const string MaxBookingDays = "max_booking_days";
        public const string PlatformCommission = "platform_commission";
        public const string AutoApproveHotels = "auto_approve_hotels";
        public const string AutoApproveReviews = "auto_approve_reviews";
        public const string MinBookingAmount = "min_booking_amount";
        public const string MaxBookingAmount = "max_booking_amount";

        // Email Settings
        public const string SmtpHost = "smtp_host";
        public const string SmtpPort = "smtp_port";
        public const string SmtpUsername = "smtp_username";
        public const string SmtpPassword = "smtp_password";
        public const string SmtpEnableSsl = "smtp_enable_ssl";
        public const string EmailFromAddress = "email_from_address";
        public const string EmailFromName = "email_from_name";

        // Payment Settings
        public const string PaymentGateway = "payment_gateway";
        public const string StripePublicKey = "stripe_public_key";
        public const string StripeSecretKey = "stripe_secret_key";
        public const string PaypalClientId = "paypal_client_id";
        public const string PaypalClientSecret = "paypal_client_secret";
        public const string PaypalSandboxMode = "paypal_sandbox_mode";

        // Security Settings
        public const string JwtExpirationMinutes = "jwt_expiration_minutes";
        public const string PasswordMinLength = "password_min_length";
        public const string RequireEmailConfirmation = "require_email_confirmation";
        public const string EnableTwoFactorAuth = "enable_two_factor_auth";
        public const string MaxLoginAttempts = "max_login_attempts";
        public const string LockoutDurationMinutes = "lockout_duration_minutes";

        // File Upload Settings
        public const string MaxFileSize = "max_file_size";
        public const string AllowedImageTypes = "allowed_image_types";
        public const string ImageStoragePath = "image_storage_path";
        public const string UseCloudStorage = "use_cloud_storage";
        public const string CloudStorageProvider = "cloud_storage_provider";

        // Notification Settings
        public const string EnableEmailNotifications = "enable_email_notifications";
        public const string EnableSmsNotifications = "enable_sms_notifications";
        public const string EnablePushNotifications = "enable_push_notifications";
        public const string NotificationRetentionDays = "notification_retention_days";

        // Analytics Settings
        public const string GoogleAnalyticsId = "google_analytics_id";
        public const string EnableAnalytics = "enable_analytics";
        public const string DataRetentionDays = "data_retention_days";

        // Cache Settings
        public const string CacheExpirationMinutes = "cache_expiration_minutes";
        public const string EnableRedisCache = "enable_redis_cache";
        public const string RedisConnectionString = "redis_connection_string";

        // API Settings
        public const string ApiRateLimit = "api_rate_limit";
        public const string ApiRateLimitWindow = "api_rate_limit_window";
        public const string EnableApiLogging = "enable_api_logging";
        public const string ApiVersion = "api_version";
    }
}
