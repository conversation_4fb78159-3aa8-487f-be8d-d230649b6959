using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace HotelMarketplace.Core.DTOs
{
    public class HotelDto
    {
        public int Id { get; set; }
        public string OwnerId { get; set; }
        public string OwnerName { get; set; }
        public string Name { get; set; }
        public string Address { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string Country { get; set; }
        public string ZipCode { get; set; }
        public string Description { get; set; }
        public double Rating { get; set; }
        public int ReviewCount { get; set; }
        public decimal StartingPrice { get; set; }
        public string Phone { get; set; }
        public string Email { get; set; }
        public string Website { get; set; }
        public List<string> Amenities { get; set; }
        public List<string> ImageUrls { get; set; }
        public bool IsActive { get; set; }
        public bool IsFeatured { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public double Latitude { get; set; }
        public double Longitude { get; set; }
        public string CheckInTime { get; set; }
        public string CheckOutTime { get; set; }
        public string CancellationPolicy { get; set; }
    }

    public class HotelCreateDto
    {
        [Required]
        [StringLength(200)]
        public string Name { get; set; }

        [Required]
        [StringLength(500)]
        public string Address { get; set; }

        [Required]
        [StringLength(100)]
        public string City { get; set; }

        [Required]
        [StringLength(100)]
        public string State { get; set; }

        [Required]
        [StringLength(100)]
        public string Country { get; set; }

        [StringLength(20)]
        public string ZipCode { get; set; }

        [Required]
        [StringLength(2000)]
        public string Description { get; set; }

        [Phone]
        public string Phone { get; set; }

        [EmailAddress]
        public string Email { get; set; }

        [Url]
        public string Website { get; set; }

        public List<string> Amenities { get; set; } = new List<string>();
        public List<string> ImageUrls { get; set; } = new List<string>();

        [Range(-90, 90)]
        public double Latitude { get; set; }

        [Range(-180, 180)]
        public double Longitude { get; set; }

        public string CheckInTime { get; set; } = "15:00";
        public string CheckOutTime { get; set; } = "11:00";
        public string CancellationPolicy { get; set; }
    }

    public class HotelUpdateDto
    {
        [Required]
        [StringLength(200)]
        public string Name { get; set; }

        [Required]
        [StringLength(500)]
        public string Address { get; set; }

        [Required]
        [StringLength(100)]
        public string City { get; set; }

        [Required]
        [StringLength(100)]
        public string State { get; set; }

        [Required]
        [StringLength(100)]
        public string Country { get; set; }

        [StringLength(20)]
        public string ZipCode { get; set; }

        [Required]
        [StringLength(2000)]
        public string Description { get; set; }

        [Phone]
        public string Phone { get; set; }

        [EmailAddress]
        public string Email { get; set; }

        [Url]
        public string Website { get; set; }

        public List<string> Amenities { get; set; }
        public List<string> ImageUrls { get; set; }

        [Range(-90, 90)]
        public double Latitude { get; set; }

        [Range(-180, 180)]
        public double Longitude { get; set; }

        public string CheckInTime { get; set; }
        public string CheckOutTime { get; set; }
        public string CancellationPolicy { get; set; }
        public bool IsActive { get; set; }
    }

    public class HotelDetailDto : HotelDto
    {
        public List<RoomDto> Rooms { get; set; }
        public List<ReviewDto> Reviews { get; set; }
    }

    public class HotelSearchDto
    {
        public string Location { get; set; }
        public decimal? MinPrice { get; set; }
        public decimal? MaxPrice { get; set; }
        public DateTime? CheckIn { get; set; }
        public DateTime? CheckOut { get; set; }
        public string RoomType { get; set; }
    }
}
