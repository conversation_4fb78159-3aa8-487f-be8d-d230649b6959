using AutoMapper;
using HotelMarketplace.Core.DTOs;
using HotelMarketplace.Core.Interfaces;
using HotelMarketplace.Core.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace HotelMarketplace.Application.Services
{
    public class HotelService : IHotelService
    {
        private readonly IHotelRepository _hotelRepository;
        private readonly IRoomRepository _roomRepository;
        private readonly IReviewRepository _reviewRepository;
        private readonly IMapper _mapper;

        public HotelService(
            IHotelRepository hotelRepository,
            IRoomRepository roomRepository,
            IReviewRepository reviewRepository,
            IMapper mapper)
        {
            _hotelRepository = hotelRepository;
            _roomRepository = roomRepository;
            _reviewRepository = reviewRepository;
            _mapper = mapper;
        }

        public async Task<ApiResponse<IEnumerable<HotelDto>>> GetAllHotelsAsync()
        {
            var hotels = await _hotelRepository.GetAllAsync();
            var hotelDtos = _mapper.Map<IEnumerable<HotelDto>>(hotels);
            return ApiResponse<IEnumerable<HotelDto>>.SuccessResponse(hotelDtos);
        }

        public async Task<ApiResponse<HotelDetailDto>> GetHotelByIdAsync(int id)
        {
            var hotel = await _hotelRepository.GetHotelWithDetailsAsync(id);
            if (hotel == null)
                return ApiResponse<HotelDetailDto>.FailureResponse("Hotel not found!");

            var hotelDetailDto = _mapper.Map<HotelDetailDto>(hotel);
            return ApiResponse<HotelDetailDto>.SuccessResponse(hotelDetailDto);
        }

        public async Task<ApiResponse<IEnumerable<HotelDto>>> GetHotelsByOwnerIdAsync(string ownerId)
        {
            var hotels = await _hotelRepository.GetHotelsByOwnerIdAsync(ownerId);
            var hotelDtos = _mapper.Map<IEnumerable<HotelDto>>(hotels);
            return ApiResponse<IEnumerable<HotelDto>>.SuccessResponse(hotelDtos);
        }

        public async Task<ApiResponse<IEnumerable<HotelDto>>> SearchHotelsAsync(HotelSearchDto searchDto)
        {
            RoomType? roomType = null;
            if (!string.IsNullOrEmpty(searchDto.RoomType) && Enum.TryParse<RoomType>(searchDto.RoomType, out var parsedRoomType))
            {
                roomType = parsedRoomType;
            }

            var hotels = await _hotelRepository.SearchHotelsAsync(
                searchDto.Location,
                searchDto.MinPrice,
                searchDto.MaxPrice,
                searchDto.CheckIn,
                searchDto.CheckOut,
                roomType
            );

            var hotelDtos = _mapper.Map<IEnumerable<HotelDto>>(hotels);
            return ApiResponse<IEnumerable<HotelDto>>.SuccessResponse(hotelDtos);
        }

        public async Task<ApiResponse<HotelDto>> CreateHotelAsync(string ownerId, HotelCreateDto createDto)
        {
            var hotel = _mapper.Map<Hotel>(createDto);
            hotel.OwnerId = ownerId;
            hotel.CreatedAt = DateTime.UtcNow;

            await _hotelRepository.AddAsync(hotel);

            var hotelDto = _mapper.Map<HotelDto>(hotel);
            return ApiResponse<HotelDto>.SuccessResponse(hotelDto, "Hotel created successfully!");
        }

        public async Task<ApiResponse<HotelDto>> UpdateHotelAsync(int id, string ownerId, HotelUpdateDto updateDto)
        {
            var hotel = await _hotelRepository.GetByIdAsync(id);
            if (hotel == null)
                return ApiResponse<HotelDto>.FailureResponse("Hotel not found!");

            if (hotel.OwnerId != ownerId)
                return ApiResponse<HotelDto>.FailureResponse("You are not authorized to update this hotel!");

            hotel.Name = updateDto.Name;
            hotel.Location = $"{updateDto.Address}, {updateDto.City}, {updateDto.State}, {updateDto.Country}";
            hotel.Description = updateDto.Description;
            hotel.UpdatedAt = DateTime.UtcNow;

            await _hotelRepository.UpdateAsync(hotel);

            var hotelDto = _mapper.Map<HotelDto>(hotel);
            return ApiResponse<HotelDto>.SuccessResponse(hotelDto, "Hotel updated successfully!");
        }

        public async Task<ApiResponse<bool>> DeleteHotelAsync(int id, string ownerId)
        {
            var hotel = await _hotelRepository.GetByIdAsync(id);
            if (hotel == null)
                return ApiResponse<bool>.FailureResponse("Hotel not found!");

            if (hotel.OwnerId != ownerId)
                return ApiResponse<bool>.FailureResponse("You are not authorized to delete this hotel!");

            await _hotelRepository.RemoveAsync(hotel);
            return ApiResponse<bool>.SuccessResponse(true, "Hotel deleted successfully!");
        }
    }
}
