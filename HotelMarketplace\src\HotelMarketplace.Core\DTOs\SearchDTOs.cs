using HotelMarketplace.Core.Models;
using System;
using System.Collections.Generic;

namespace HotelMarketplace.Core.DTOs
{
    public class AdvancedHotelSearchDto : HotelSearchDto
    {
        public List<string>? Amenities { get; set; }
        public double? MinRating { get; set; }
        public double? MaxRating { get; set; }
        public new decimal? MinPrice { get; set; }
        public new decimal? MaxPrice { get; set; }
        public List<RoomType>? RoomTypes { get; set; }
        public bool? HasParking { get; set; }
        public bool? HasWifi { get; set; }
        public bool? HasPool { get; set; }
        public bool? HasGym { get; set; }
        public bool? HasSpa { get; set; }
        public bool? HasRestaurant { get; set; }
        public bool? PetFriendly { get; set; }
        public string? SortBy { get; set; } // price, rating, distance, popularity
        public string? SortOrder { get; set; } // asc, desc
        public double? Latitude { get; set; }
        public double? Longitude { get; set; }
        public double? RadiusKm { get; set; }
        public string? PropertyType { get; set; } // hotel, resort, apartment, etc.
    }

    public class RoomSearchDto
    {
        public string? Location { get; set; }
        public DateTime? CheckIn { get; set; }
        public DateTime? CheckOut { get; set; }
        public int Guests { get; set; } = 1;
        public int Rooms { get; set; } = 1;
        public List<RoomType>? RoomTypes { get; set; }
        public decimal? MinPrice { get; set; }
        public decimal? MaxPrice { get; set; }
        public List<string>? Amenities { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 10;
        public string? SortBy { get; set; }
        public string? SortOrder { get; set; }
    }

    public class SearchSuggestionDto
    {
        public string Text { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty; // location, hotel, amenity
        public int Count { get; set; }
        public string? ImageUrl { get; set; }
    }

    public class AutocompleteDto
    {
        public string Value { get; set; } = string.Empty;
        public string Label { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public string? Category { get; set; }
        public string? ImageUrl { get; set; }
        public int? ResultCount { get; set; }
    }

    public class DestinationDto
    {
        public string Name { get; set; } = string.Empty;
        public string Country { get; set; } = string.Empty;
        public string? State { get; set; }
        public int HotelCount { get; set; }
        public double AverageRating { get; set; }
        public decimal AveragePrice { get; set; }
        public string? ImageUrl { get; set; }
        public string? Description { get; set; }
        public bool IsPopular { get; set; }
        public double? Latitude { get; set; }
        public double? Longitude { get; set; }
    }

    public class TrendingSearchDto
    {
        public string Query { get; set; } = string.Empty;
        public int SearchCount { get; set; }
        public double GrowthRate { get; set; }
        public string Category { get; set; } = string.Empty;
    }

    public class SearchFiltersDto
    {
        public List<FilterOptionDto> Locations { get; set; } = new();
        public List<FilterOptionDto> Amenities { get; set; } = new();
        public List<FilterOptionDto> RoomTypes { get; set; } = new();
        public List<FilterOptionDto> PropertyTypes { get; set; } = new();
        public PriceRangeDto PriceRange { get; set; } = new();
        public RatingRangeDto RatingRange { get; set; } = new();
        public List<FilterOptionDto> StarRatings { get; set; } = new();
    }

    public class FilterOptionDto
    {
        public string Value { get; set; } = string.Empty;
        public string Label { get; set; } = string.Empty;
        public int Count { get; set; }
        public bool IsSelected { get; set; }
    }

    public class PriceRangeDto
    {
        public decimal Min { get; set; }
        public decimal Max { get; set; }
        public decimal Step { get; set; }
        public string Currency { get; set; } = "USD";
    }

    public class RatingRangeDto
    {
        public double Min { get; set; }
        public double Max { get; set; }
        public double Step { get; set; }
    }

    public class SaveSearchDto
    {
        public string UserId { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string SearchQuery { get; set; } = string.Empty;
        public string? Location { get; set; }
        public DateTime? CheckIn { get; set; }
        public DateTime? CheckOut { get; set; }
        public int Guests { get; set; }
        public int Rooms { get; set; }
        public string? Filters { get; set; } // JSON string of applied filters
        public bool EnableAlerts { get; set; }
    }

    public class SavedSearchDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string SearchQuery { get; set; } = string.Empty;
        public string? Location { get; set; }
        public DateTime? CheckIn { get; set; }
        public DateTime? CheckOut { get; set; }
        public int Guests { get; set; }
        public int Rooms { get; set; }
        public string? Filters { get; set; }
        public bool EnableAlerts { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? LastUsed { get; set; }
        public int UseCount { get; set; }
    }

    public class SearchTrackingDto
    {
        public string? UserId { get; set; }
        public string Query { get; set; } = string.Empty;
        public string? Location { get; set; }
        public DateTime? CheckIn { get; set; }
        public DateTime? CheckOut { get; set; }
        public int Guests { get; set; }
        public int Rooms { get; set; }
        public int ResultCount { get; set; }
        public string? Filters { get; set; }
        public string? SortBy { get; set; }
        public string? UserAgent { get; set; }
        public string? IpAddress { get; set; }
        public string? SessionId { get; set; }
    }

    public class SearchAnalyticsDto
    {
        public int TotalSearches { get; set; }
        public int UniqueUsers { get; set; }
        public List<PopularSearchDto> PopularQueries { get; set; } = new();
        public List<PopularDestinationDto> PopularDestinations { get; set; } = new();
        public List<SearchTrendDto> SearchTrends { get; set; } = new();
        public double AverageResultsPerSearch { get; set; }
        public double SearchToBookingConversion { get; set; }
    }

    public class PopularSearchDto
    {
        public string Query { get; set; } = string.Empty;
        public int Count { get; set; }
        public double Percentage { get; set; }
    }

    public class PopularDestinationDto
    {
        public string Destination { get; set; } = string.Empty;
        public int SearchCount { get; set; }
        public int BookingCount { get; set; }
        public double ConversionRate { get; set; }
    }

    public class SearchTrendDto
    {
        public DateTime Date { get; set; }
        public int SearchCount { get; set; }
        public int UniqueUsers { get; set; }
        public double AverageResultCount { get; set; }
    }
}
