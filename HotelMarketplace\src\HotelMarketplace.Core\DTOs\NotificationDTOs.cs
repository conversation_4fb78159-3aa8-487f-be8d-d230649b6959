using HotelMarketplace.Core.Models;
using System;
using System.Collections.Generic;

namespace HotelMarketplace.Core.DTOs
{
    public class NotificationPreferencesDto
    {
        public bool EmailNotifications { get; set; } = true;
        public bool PushNotifications { get; set; } = true;
        public bool SmsNotifications { get; set; } = false;
        public bool BookingConfirmations { get; set; } = true;
        public bool BookingReminders { get; set; } = true;
        public bool PaymentNotifications { get; set; } = true;
        public bool PromotionalEmails { get; set; } = true;
        public bool NewsletterSubscription { get; set; } = false;
        public bool ReviewReminders { get; set; } = true;
        public bool PriceAlerts { get; set; } = false;
        public bool SystemUpdates { get; set; } = true;
        public bool SecurityAlerts { get; set; } = true;
        public string? PreferredLanguage { get; set; }
        public string? TimeZone { get; set; }
        public int? QuietHoursStart { get; set; } // Hour of day (0-23)
        public int? QuietHoursEnd { get; set; } // Hour of day (0-23)
    }

    public class TestNotificationDto
    {
        public string UserId { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public NotificationType Type { get; set; }
        public string? ActionUrl { get; set; }
        public bool SendEmail { get; set; }
        public bool SendPush { get; set; }
        public bool SendSms { get; set; }
    }

    public class NotificationTemplateDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Subject { get; set; } = string.Empty;
        public string Body { get; set; } = string.Empty;
        public string? SmsTemplate { get; set; }
        public string? PushTemplate { get; set; }
        public NotificationType Type { get; set; }
        public bool IsActive { get; set; }
        public string? Language { get; set; }
        public Dictionary<string, string> Variables { get; set; } = new();
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    public class NotificationAnalyticsDto
    {
        public int TotalSent { get; set; }
        public int TotalDelivered { get; set; }
        public int TotalOpened { get; set; }
        public int TotalClicked { get; set; }
        public double DeliveryRate { get; set; }
        public double OpenRate { get; set; }
        public double ClickRate { get; set; }
        public List<NotificationTypeStatsDto> TypeStats { get; set; } = new();
        public List<DailyNotificationStatsDto> DailyStats { get; set; } = new();
        public List<ChannelStatsDto> ChannelStats { get; set; } = new();
    }

    public class NotificationTypeStatsDto
    {
        public NotificationType Type { get; set; }
        public int Sent { get; set; }
        public int Delivered { get; set; }
        public int Opened { get; set; }
        public int Clicked { get; set; }
        public double DeliveryRate { get; set; }
        public double OpenRate { get; set; }
        public double ClickRate { get; set; }
    }

    public class DailyNotificationStatsDto
    {
        public DateTime Date { get; set; }
        public int Sent { get; set; }
        public int Delivered { get; set; }
        public int Opened { get; set; }
        public int Clicked { get; set; }
    }

    public class ChannelStatsDto
    {
        public string Channel { get; set; } = string.Empty; // Email, Push, SMS
        public int Sent { get; set; }
        public int Delivered { get; set; }
        public int Failed { get; set; }
        public double SuccessRate { get; set; }
    }

    public class NotificationLogDto
    {
        public int Id { get; set; }
        public string UserId { get; set; } = string.Empty;
        public string UserName { get; set; } = string.Empty;
        public string UserEmail { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public NotificationType Type { get; set; }
        public string Channel { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty; // Sent, Delivered, Failed, Opened, Clicked
        public DateTime SentAt { get; set; }
        public DateTime? DeliveredAt { get; set; }
        public DateTime? OpenedAt { get; set; }
        public DateTime? ClickedAt { get; set; }
        public string? ErrorMessage { get; set; }
        public string? ActionUrl { get; set; }
    }

    public class DeviceTokenDto
    {
        public string UserId { get; set; } = string.Empty;
        public string Token { get; set; } = string.Empty;
        public string Platform { get; set; } = string.Empty; // iOS, Android, Web
        public string? DeviceInfo { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? LastUsed { get; set; }
    }

    public class EmailQueueDto
    {
        public int Id { get; set; }
        public string ToEmail { get; set; } = string.Empty;
        public string? ToName { get; set; }
        public string Subject { get; set; } = string.Empty;
        public string Body { get; set; } = string.Empty;
        public bool IsHtml { get; set; }
        public string? TemplateName { get; set; }
        public string? TemplateData { get; set; }
        public int Priority { get; set; } = 1; // 1 = High, 2 = Normal, 3 = Low
        public int RetryCount { get; set; }
        public int MaxRetries { get; set; } = 3;
        public DateTime CreatedAt { get; set; }
        public DateTime? ScheduledAt { get; set; }
        public DateTime? SentAt { get; set; }
        public string Status { get; set; } = "Pending"; // Pending, Sent, Failed, Cancelled
        public string? ErrorMessage { get; set; }
    }

    public class SmsQueueDto
    {
        public int Id { get; set; }
        public string PhoneNumber { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public int Priority { get; set; } = 1;
        public int RetryCount { get; set; }
        public int MaxRetries { get; set; } = 3;
        public DateTime CreatedAt { get; set; }
        public DateTime? ScheduledAt { get; set; }
        public DateTime? SentAt { get; set; }
        public string Status { get; set; } = "Pending";
        public string? ErrorMessage { get; set; }
        public string? ProviderId { get; set; }
    }

    public class PushNotificationDto
    {
        public string Title { get; set; } = string.Empty;
        public string Body { get; set; } = string.Empty;
        public string? Icon { get; set; }
        public string? Image { get; set; }
        public string? ActionUrl { get; set; }
        public Dictionary<string, string>? Data { get; set; }
        public List<string> DeviceTokens { get; set; } = new();
        public string? Sound { get; set; }
        public int? Badge { get; set; }
        public bool? Silent { get; set; }
        public DateTime? ScheduledAt { get; set; }
        public string? CollapseKey { get; set; }
        public int? TimeToLive { get; set; }
    }
}
