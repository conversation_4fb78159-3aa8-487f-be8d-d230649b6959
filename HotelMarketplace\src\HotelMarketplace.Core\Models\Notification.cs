using HotelMarketplace.Core.DTOs;
using System;
using System.ComponentModel.DataAnnotations;

namespace HotelMarketplace.Core.Models
{
    public class Notification
    {
        public int Id { get; set; }

        [Required]
        [MaxLength(200)]
        public string Title { get; set; } = string.Empty;

        [Required]
        [MaxLength(1000)]
        public string Message { get; set; } = string.Empty;

        public NotificationType Type { get; set; }

        public bool IsRead { get; set; }

        public DateTime CreatedAt { get; set; }

        public DateTime? ReadAt { get; set; }

        // Foreign Keys
        public string? UserId { get; set; }
        public string? CreatedByUserId { get; set; }

        // Navigation Properties
        public User? User { get; set; }
        public User? CreatedByUser { get; set; }

        // Additional Properties
        public string? ActionUrl { get; set; }
        public string? ActionText { get; set; }
        public DateTime? ExpiresAt { get; set; }
        public bool IsBroadcast { get; set; }
        public UserRole? TargetRole { get; set; }
    }
}
