{"ConnectionStrings": {"DefaultConnection": "Server=HOSSEINPC;Database=HotelMarketplace;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true;"}, "Jwt": {"Key": "ThisIsMySecretKeyForHotelMarketplaceApplication12345", "Issuer": "HotelMarketplaceAPI", "Audience": "HotelMarketplaceClient", "DurationInMinutes": 180}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*"}