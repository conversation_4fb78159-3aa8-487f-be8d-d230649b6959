<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hotel Marketplace Demo - Features Overview</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="css/output.css" rel="stylesheet">
    <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
</head>
<body class="bg-gray-50 font-sans">
    <!-- Header -->
    <header class="bg-white shadow-sm">
        <div class="container mx-auto px-4 py-3">
            <nav class="flex items-center justify-between">
                <a href="index.html" class="flex items-center">
                    <span class="text-2xl font-bold text-primary-600">HotelMarketplace</span>
                </a>
                <div class="hidden md:flex space-x-6">
                    <a href="index.html" class="text-gray-800 hover:text-primary-600 font-medium">Home</a>
                    <a href="hotels.html" class="text-gray-800 hover:text-primary-600 font-medium">Hotels</a>
                    <a href="demo.html" class="text-primary-600 font-medium">Demo</a>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="login.html" class="btn btn-outline">Login</a>
                    <a href="register.html" class="btn btn-primary">Sign Up</a>
                </div>
            </nav>
        </div>
    </header>

    <!-- Demo Content -->
    <main class="container mx-auto px-4 py-8">
        <!-- Hero Section -->
        <section class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-900 mb-4">Hotel Marketplace Demo</h1>
            <p class="text-xl text-gray-600 mb-8">Explore all the features of our comprehensive hotel booking platform</p>
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 max-w-4xl mx-auto">
                <h2 class="text-lg font-semibold text-blue-900 mb-4">🚀 Demo Credentials</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
                    <div class="bg-white p-4 rounded border">
                        <h3 class="font-medium text-blue-900 mb-2">Admin Access</h3>
                        <p class="text-sm text-gray-600">Email: <code class="bg-gray-100 px-2 py-1 rounded"><EMAIL></code></p>
                        <p class="text-sm text-gray-600">Password: <code class="bg-gray-100 px-2 py-1 rounded">admin123</code></p>
                    </div>
                    <div class="bg-white p-4 rounded border">
                        <h3 class="font-medium text-blue-900 mb-2">Customer Access</h3>
                        <p class="text-sm text-gray-600">Email: <code class="bg-gray-100 px-2 py-1 rounded"><EMAIL></code></p>
                        <p class="text-sm text-gray-600">Password: <code class="bg-gray-100 px-2 py-1 rounded">user123</code></p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Features Grid -->
        <section class="mb-12">
            <h2 class="text-3xl font-bold text-center text-gray-900 mb-8">Platform Features</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Hotel Search & Booking -->
                <div class="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                        <i class="fas fa-search text-blue-600 text-xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3">Hotel Search & Booking</h3>
                    <p class="text-gray-600 mb-4">Advanced search with filters, real-time availability, and seamless booking process.</p>
                    <a href="hotels.html" class="btn btn-outline btn-sm">Try Hotel Search</a>
                </div>

                <!-- User Authentication -->
                <div class="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                        <i class="fas fa-user-shield text-green-600 text-xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3">User Authentication</h3>
                    <p class="text-gray-600 mb-4">Secure login/register system with role-based access control.</p>
                    <a href="login.html" class="btn btn-outline btn-sm">Try Login</a>
                </div>

                <!-- Admin Dashboard -->
                <div class="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
                        <i class="fas fa-tachometer-alt text-purple-600 text-xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3">Admin Dashboard</h3>
                    <p class="text-gray-600 mb-4">Comprehensive admin panel with analytics, user management, and hotel approvals.</p>
                    <a href="admin-dashboard.html" class="btn btn-outline btn-sm">View Dashboard</a>
                </div>

                <!-- Hotel Details -->
                <div class="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow">
                    <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mb-4">
                        <i class="fas fa-hotel text-yellow-600 text-xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3">Hotel Details</h3>
                    <p class="text-gray-600 mb-4">Detailed hotel pages with image galleries, amenities, and room selection.</p>
                    <a href="hotel-details.html?id=1" class="btn btn-outline btn-sm">View Hotel</a>
                </div>

                <!-- Booking Confirmation -->
                <div class="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow">
                    <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-4">
                        <i class="fas fa-calendar-check text-red-600 text-xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3">Booking Management</h3>
                    <p class="text-gray-600 mb-4">Professional booking confirmations with detailed information and actions.</p>
                    <a href="booking-confirmation.html?id=1" class="btn btn-outline btn-sm">View Booking</a>
                </div>

                <!-- Responsive Design -->
                <div class="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow">
                    <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mb-4">
                        <i class="fas fa-mobile-alt text-indigo-600 text-xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3">Responsive Design</h3>
                    <p class="text-gray-600 mb-4">Mobile-first design that works perfectly on all devices and screen sizes.</p>
                    <button class="btn btn-outline btn-sm" onclick="alert('Try resizing your browser window!')">Test Responsive</button>
                </div>
            </div>
        </section>

        <!-- Technical Features -->
        <section class="mb-12">
            <h2 class="text-3xl font-bold text-center text-gray-900 mb-8">Technical Features</h2>
            <div class="bg-white rounded-lg shadow-sm p-8">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-code text-blue-600 text-2xl"></i>
                        </div>
                        <h3 class="font-semibold mb-2">Clean Architecture</h3>
                        <p class="text-sm text-gray-600">Modular C# backend with clean separation of concerns</p>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-database text-green-600 text-2xl"></i>
                        </div>
                        <h3 class="font-semibold mb-2">Entity Framework</h3>
                        <p class="text-sm text-gray-600">Code-first approach with SQLite database</p>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-paint-brush text-purple-600 text-2xl"></i>
                        </div>
                        <h3 class="font-semibold mb-2">Tailwind CSS</h3>
                        <p class="text-sm text-gray-600">Modern utility-first CSS framework</p>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-shield-alt text-yellow-600 text-2xl"></i>
                        </div>
                        <h3 class="font-semibold mb-2">JWT Authentication</h3>
                        <p class="text-sm text-gray-600">Secure token-based authentication</p>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-chart-line text-red-600 text-2xl"></i>
                        </div>
                        <h3 class="font-semibold mb-2">Analytics Dashboard</h3>
                        <p class="text-sm text-gray-600">Real-time charts and reporting</p>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-rocket text-indigo-600 text-2xl"></i>
                        </div>
                        <h3 class="font-semibold mb-2">Mock Data</h3>
                        <p class="text-sm text-gray-600">Fully functional with mock data for demo</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Quick Actions -->
        <section class="text-center">
            <h2 class="text-3xl font-bold text-gray-900 mb-8">Quick Demo Actions</h2>
            <div class="flex flex-wrap justify-center gap-4">
                <a href="hotels.html" class="btn btn-primary">Browse Hotels</a>
                <a href="login.html" class="btn btn-secondary">Login as Admin</a>
                <a href="hotel-details.html?id=1" class="btn btn-outline">View Hotel Details</a>
                <a href="admin-dashboard.html" class="btn btn-outline">Admin Dashboard</a>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-8 mt-12">
        <div class="container mx-auto px-4 text-center">
            <p>&copy; 2024 Hotel Marketplace. Built with ❤️ using C# .NET Core, Entity Framework, and Tailwind CSS.</p>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="js/api.js"></script>
    <script src="js/utils.js"></script>
</body>
</html>
