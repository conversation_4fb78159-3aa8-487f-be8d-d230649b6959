using HotelMarketplace.Core.DTOs;
using HotelMarketplace.Core.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace HotelMarketplace.Core.Interfaces
{
    public interface INotificationService
    {
        // User Notifications
        Task<ApiResponse<PagedResult<NotificationDto>>> GetUserNotificationsAsync(string userId, int page, int pageSize, bool unreadOnly = false);
        Task<ApiResponse<int>> GetUnreadCountAsync(string userId);
        Task<ApiResponse<bool>> MarkAsReadAsync(int notificationId, string userId);
        Task<ApiResponse<bool>> MarkAllAsReadAsync(string userId);
        Task<ApiResponse<bool>> DeleteNotificationAsync(int notificationId, string userId);
        Task<ApiResponse<bool>> ClearAllNotificationsAsync(string userId);

        // Send Notifications
        Task<ApiResponse<bool>> SendNotificationAsync(string userId, string title, string message, NotificationType type, string? actionUrl = null);
        Task<ApiResponse<bool>> SendBulkNotificationAsync(List<string> userIds, string title, string message, NotificationType type, string? actionUrl = null);
        Task<ApiResponse<bool>> SendRoleBasedNotificationAsync(UserRole role, string title, string message, NotificationType type, string? actionUrl = null);
        Task<ApiResponse<bool>> BroadcastNotificationAsync(string title, string message, NotificationType type, string? actionUrl = null);

        // Email Notifications
        Task<ApiResponse<bool>> SendEmailNotificationAsync(string email, string subject, string body, string? templateName = null);
        Task<ApiResponse<bool>> SendBookingConfirmationEmailAsync(int bookingId);
        Task<ApiResponse<bool>> SendBookingCancellationEmailAsync(int bookingId);
        Task<ApiResponse<bool>> SendPaymentConfirmationEmailAsync(int paymentId);
        Task<ApiResponse<bool>> SendWelcomeEmailAsync(string userId);
        Task<ApiResponse<bool>> SendPasswordResetEmailAsync(string email, string resetToken);

        // Push Notifications
        Task<ApiResponse<bool>> SendPushNotificationAsync(string userId, string title, string message, Dictionary<string, string>? data = null);
        Task<ApiResponse<bool>> RegisterDeviceTokenAsync(string userId, string deviceToken, string platform);
        Task<ApiResponse<bool>> UnregisterDeviceTokenAsync(string userId, string deviceToken);

        // SMS Notifications
        Task<ApiResponse<bool>> SendSmsNotificationAsync(string phoneNumber, string message);
        Task<ApiResponse<bool>> SendBookingReminderSmsAsync(int bookingId);

        // Notification Preferences
        Task<ApiResponse<NotificationPreferencesDto>> GetNotificationPreferencesAsync(string userId);
        Task<ApiResponse<bool>> UpdateNotificationPreferencesAsync(string userId, NotificationPreferencesDto preferences);

        // Templates
        Task<ApiResponse<IEnumerable<NotificationTemplateDto>>> GetNotificationTemplatesAsync();
        Task<ApiResponse<NotificationTemplateDto>> GetNotificationTemplateAsync(string templateName);
        Task<ApiResponse<bool>> UpdateNotificationTemplateAsync(int templateId, NotificationTemplateDto template);
        Task<ApiResponse<bool>> CreateNotificationTemplateAsync(NotificationTemplateDto template);

        // System Notifications
        Task<ApiResponse<bool>> SendSystemNotificationAsync(string title, string message, NotificationType type);
        Task<ApiResponse<bool>> SendTestNotificationAsync(TestNotificationDto testNotification);

        // Notification History and Analytics
        Task<ApiResponse<NotificationAnalyticsDto>> GetNotificationAnalyticsAsync(int days);
        Task<ApiResponse<PagedResult<NotificationLogDto>>> GetNotificationHistoryAsync(int page, int pageSize, string? userId = null);

        // Cleanup
        Task<ApiResponse<bool>> CleanupOldNotificationsAsync(int retentionDays);
    }
}
