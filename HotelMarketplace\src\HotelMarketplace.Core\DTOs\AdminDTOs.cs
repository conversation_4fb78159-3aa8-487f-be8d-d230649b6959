using HotelMarketplace.Core.Models;
using System;
using System.Collections.Generic;

namespace HotelMarketplace.Core.DTOs
{
    public class AdminDashboardDto
    {
        public int TotalUsers { get; set; }
        public int TotalHotels { get; set; }
        public int TotalBookings { get; set; }
        public int PendingReviews { get; set; }
        public int PendingHotels { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal MonthlyRevenue { get; set; }
        public int ActiveBookings { get; set; }
        public int NewUsersThisMonth { get; set; }
        public List<RecentActivityDto> RecentActivities { get; set; } = new();
        public List<TopHotelDto> TopHotels { get; set; } = new();
    }

    public class RecentActivityDto
    {
        public string Type { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
        public string UserId { get; set; } = string.Empty;
        public string UserName { get; set; } = string.Empty;
    }

    public class TopHotelDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Location { get; set; } = string.Empty;
        public double Rating { get; set; }
        public int BookingCount { get; set; }
        public decimal Revenue { get; set; }
    }

    public class UserStatusUpdateDto
    {
        public bool IsActive { get; set; }
        public bool IsEmailConfirmed { get; set; }
        public string? Reason { get; set; }
    }

    public class HotelRejectionDto
    {
        public string Reason { get; set; } = string.Empty;
    }

    public class BroadcastNotificationDto
    {
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public NotificationType Type { get; set; }
        public List<string>? TargetUserIds { get; set; }
        public UserRole? TargetRole { get; set; }
    }

    public class AnalyticsOverviewDto
    {
        public List<DailyStatsDto> DailyStats { get; set; } = new();
        public BookingTrendsDto BookingTrends { get; set; } = new();
        public RevenueTrendsDto RevenueTrends { get; set; } = new();
        public UserGrowthDto UserGrowth { get; set; } = new();
        public PopularDestinationsDto PopularDestinations { get; set; } = new();
    }

    public class DailyStatsDto
    {
        public DateTime Date { get; set; }
        public int NewUsers { get; set; }
        public int NewBookings { get; set; }
        public decimal Revenue { get; set; }
        public int NewHotels { get; set; }
    }

    public class BookingTrendsDto
    {
        public int TotalBookings { get; set; }
        public int CompletedBookings { get; set; }
        public int CancelledBookings { get; set; }
        public int PendingBookings { get; set; }
        public double AverageBookingValue { get; set; }
        public List<MonthlyBookingDto> MonthlyData { get; set; } = new();
    }

    public class MonthlyBookingDto
    {
        public string Month { get; set; } = string.Empty;
        public int BookingCount { get; set; }
        public decimal Revenue { get; set; }
    }

    public class RevenueTrendsDto
    {
        public decimal TotalRevenue { get; set; }
        public decimal MonthlyGrowth { get; set; }
        public decimal AverageOrderValue { get; set; }
        public List<MonthlyRevenueDto> MonthlyRevenue { get; set; } = new();
    }

    public class MonthlyRevenueDto
    {
        public string Month { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public int TransactionCount { get; set; }
    }

    public class UserGrowthDto
    {
        public int TotalUsers { get; set; }
        public int NewUsersThisMonth { get; set; }
        public double GrowthRate { get; set; }
        public List<UserTypeStatsDto> UserTypeStats { get; set; } = new();
    }

    public class UserTypeStatsDto
    {
        public UserRole Role { get; set; }
        public int Count { get; set; }
        public double Percentage { get; set; }
    }

    public class PopularDestinationsDto
    {
        public List<DestinationStatsDto> Destinations { get; set; } = new();
    }

    public class DestinationStatsDto
    {
        public string Location { get; set; } = string.Empty;
        public int HotelCount { get; set; }
        public int BookingCount { get; set; }
        public decimal Revenue { get; set; }
        public double AverageRating { get; set; }
    }

    public class SystemHealthDto
    {
        public bool DatabaseStatus { get; set; }
        public bool ApiStatus { get; set; }
        public bool EmailServiceStatus { get; set; }
        public bool PaymentServiceStatus { get; set; }
        public double CpuUsage { get; set; }
        public double MemoryUsage { get; set; }
        public double DiskUsage { get; set; }
        public int ActiveConnections { get; set; }
        public DateTime LastBackup { get; set; }
        public List<ServiceStatusDto> Services { get; set; } = new();
    }

    public class ServiceStatusDto
    {
        public string Name { get; set; } = string.Empty;
        public bool IsHealthy { get; set; }
        public string Status { get; set; } = string.Empty;
        public DateTime LastChecked { get; set; }
        public string? ErrorMessage { get; set; }
    }

    public class RevenueReportDto
    {
        public decimal TotalRevenue { get; set; }
        public decimal PeriodRevenue { get; set; }
        public double GrowthRate { get; set; }
        public List<MonthlyRevenueDto> MonthlyBreakdown { get; set; } = new();
        public List<HotelRevenueDto> TopPerformingHotels { get; set; } = new();
        public List<PaymentMethodStatsDto> PaymentMethodStats { get; set; } = new();
    }

    public class HotelRevenueDto
    {
        public int HotelId { get; set; }
        public string HotelName { get; set; } = string.Empty;
        public string Location { get; set; } = string.Empty;
        public decimal Revenue { get; set; }
        public int BookingCount { get; set; }
        public double AverageRating { get; set; }
    }

    public class PaymentMethodStatsDto
    {
        public PaymentMethod Method { get; set; }
        public int TransactionCount { get; set; }
        public decimal TotalAmount { get; set; }
        public double Percentage { get; set; }
    }

    public class BookingReportDto
    {
        public int TotalBookings { get; set; }
        public int CompletedBookings { get; set; }
        public int CancelledBookings { get; set; }
        public int PendingBookings { get; set; }
        public double CancellationRate { get; set; }
        public double AverageBookingValue { get; set; }
        public List<MonthlyBookingDto> MonthlyBookings { get; set; } = new();
        public List<PopularRoomTypeDto> PopularRoomTypes { get; set; } = new();
    }

    public class PopularRoomTypeDto
    {
        public RoomType RoomType { get; set; }
        public int BookingCount { get; set; }
        public decimal Revenue { get; set; }
        public double AveragePrice { get; set; }
    }

    public class HotelReportDto
    {
        public int TotalHotels { get; set; }
        public int ActiveHotels { get; set; }
        public int PendingApproval { get; set; }
        public int SuspendedHotels { get; set; }
        public double AverageRating { get; set; }
        public List<LocationStatsDto> LocationStats { get; set; } = new();
        public List<HotelPerformanceDto> TopPerformers { get; set; } = new();
    }

    public class LocationStatsDto
    {
        public string Location { get; set; } = string.Empty;
        public int HotelCount { get; set; }
        public double AverageRating { get; set; }
        public int TotalRooms { get; set; }
    }

    public class HotelPerformanceDto
    {
        public int HotelId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Location { get; set; } = string.Empty;
        public double Rating { get; set; }
        public int BookingCount { get; set; }
        public decimal Revenue { get; set; }
        public double OccupancyRate { get; set; }
    }

    public class UserReportDto
    {
        public int TotalUsers { get; set; }
        public int ActiveUsers { get; set; }
        public int NewUsersThisMonth { get; set; }
        public List<UserTypeStatsDto> UserTypeBreakdown { get; set; } = new();
        public List<UserActivityDto> TopActiveUsers { get; set; } = new();
    }

    public class UserActivityDto
    {
        public string UserId { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public UserRole Role { get; set; }
        public int BookingCount { get; set; }
        public decimal TotalSpent { get; set; }
        public DateTime LastActivity { get; set; }
    }

    public class NotificationDto
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public NotificationType Type { get; set; }
        public bool IsRead { get; set; }
        public DateTime CreatedAt { get; set; }
        public string? UserId { get; set; }
    }

    public class SystemLogDto
    {
        public int Id { get; set; }
        public string Level { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string? Exception { get; set; }
        public DateTime Timestamp { get; set; }
        public string? UserId { get; set; }
        public string? Action { get; set; }
    }

    public class SystemSettingsDto
    {
        public string SiteName { get; set; } = string.Empty;
        public string SiteDescription { get; set; } = string.Empty;
        public string ContactEmail { get; set; } = string.Empty;
        public string SupportPhone { get; set; } = string.Empty;
        public bool MaintenanceMode { get; set; }
        public string MaintenanceMessage { get; set; } = string.Empty;
        public int MaxBookingDays { get; set; }
        public decimal PlatformCommission { get; set; }
        public bool AutoApproveHotels { get; set; }
        public bool AutoApproveReviews { get; set; }
        public Dictionary<string, string> EmailTemplates { get; set; } = new();
    }

    public class PromotionDto
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public decimal DiscountPercentage { get; set; }
        public decimal? DiscountAmount { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public bool IsActive { get; set; }
        public List<int>? ApplicableHotelIds { get; set; }
        public RoomType? ApplicableRoomType { get; set; }
    }

    public enum NotificationType
    {
        Info,
        Warning,
        Error,
        Success,
        Promotion,
        System
    }
}
