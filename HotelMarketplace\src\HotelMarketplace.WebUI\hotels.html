<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hotels - Hotel Marketplace</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="css/output.css" rel="stylesheet">
</head>
<body class="bg-gray-50 font-sans">
    <!-- Alert Container -->
    <div id="alert-container" class="fixed top-4 right-4 z-50 w-80"></div>

    <!-- Header/Navigation -->
    <header class="bg-white shadow-sm">
        <div class="container mx-auto px-4 py-3">
            <nav class="flex items-center justify-between">
                <a href="index.html" class="flex items-center">
                    <span class="text-2xl font-bold text-primary-600">HotelMarketplace</span>
                </a>

                <div class="hidden md:flex space-x-6">
                    <a href="index.html" class="text-gray-800 hover:text-primary-600 font-medium">Home</a>
                    <a href="hotels.html" class="text-primary-600 font-medium">Hotels</a>
                    <a href="about.html" class="text-gray-800 hover:text-primary-600 font-medium">About</a>
                    <a href="contact.html" class="text-gray-800 hover:text-primary-600 font-medium">Contact</a>
                </div>

                <div id="nav-auth-section" class="flex items-center space-x-4">
                    <!-- Will be populated by utils.updateNavigation() -->
                </div>

                <!-- Mobile menu button -->
                <button id="mobile-menu-button" class="md:hidden">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                </button>
            </nav>

            <!-- Mobile menu -->
            <div id="mobile-menu" class="hidden md:hidden mt-4 pb-4">
                <a href="index.html" class="block py-2 text-gray-800 hover:text-primary-600">Home</a>
                <a href="hotels.html" class="block py-2 text-primary-600">Hotels</a>
                <a href="about.html" class="block py-2 text-gray-800 hover:text-primary-600">About</a>
                <a href="contact.html" class="block py-2 text-gray-800 hover:text-primary-600">Contact</a>
            </div>
        </div>
    </header>

    <!-- Hero Search Section -->
    <section class="bg-gradient-to-r from-primary-600 to-primary-800 text-white py-12">
        <div class="container mx-auto px-4">
            <div class="text-center mb-8">
                <h1 class="text-4xl font-bold mb-4">Find Your Perfect Hotel</h1>
                <p class="text-xl text-primary-100">Discover amazing places to stay around the world</p>
            </div>

            <!-- Main Search Form -->
            <div class="bg-white rounded-lg shadow-lg p-6 max-w-6xl mx-auto">
                <form id="main-search-form" class="grid grid-cols-1 md:grid-cols-5 gap-4">
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Where are you going?</label>
                        <input type="text" id="destination" name="destination" class="input" placeholder="City, hotel, or landmark">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Check-in</label>
                        <input type="date" id="check-in" name="checkIn" class="input">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Check-out</label>
                        <input type="date" id="check-out" name="checkOut" class="input">
                    </div>
                    <div class="flex items-end">
                        <button type="submit" class="btn btn-primary w-full h-12">
                            <i class="fas fa-search mr-2"></i>
                            Search
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </section>

    <!-- Hotel Listing Section -->
    <section class="container mx-auto px-4 py-8">
        <div class="flex flex-col lg:flex-row gap-8">
            <!-- Sidebar Filters -->
            <aside class="lg:w-1/4">
                <div class="bg-white rounded-lg shadow-sm p-6 sticky top-4">
                    <h3 class="text-lg font-semibold mb-4">Filter Results</h3>

                    <!-- Price Range -->
                    <div class="mb-6">
                        <h4 class="font-medium mb-3">Price per night</h4>
                        <div class="grid grid-cols-2 gap-2">
                            <input type="number" id="min-price" placeholder="Min" class="input text-sm">
                            <input type="number" id="max-price" placeholder="Max" class="input text-sm">
                        </div>
                    </div>

                    <!-- Star Rating -->
                    <div class="mb-6">
                        <h4 class="font-medium mb-3">Star Rating</h4>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" name="rating" value="5" class="mr-2">
                                <span class="flex items-center">
                                    <span class="text-yellow-400">★★★★★</span>
                                    <span class="ml-2 text-sm">5 stars</span>
                                </span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="rating" value="4" class="mr-2">
                                <span class="flex items-center">
                                    <span class="text-yellow-400">★★★★</span>
                                    <span class="ml-2 text-sm">4 stars & up</span>
                                </span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="rating" value="3" class="mr-2">
                                <span class="flex items-center">
                                    <span class="text-yellow-400">★★★</span>
                                    <span class="ml-2 text-sm">3 stars & up</span>
                                </span>
                            </label>
                        </div>
                    </div>

                    <!-- Amenities -->
                    <div class="mb-6">
                        <h4 class="font-medium mb-3">Amenities</h4>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" name="amenity" value="wifi" class="mr-2">
                                <span class="text-sm">Free WiFi</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="amenity" value="pool" class="mr-2">
                                <span class="text-sm">Swimming Pool</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="amenity" value="gym" class="mr-2">
                                <span class="text-sm">Fitness Center</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="amenity" value="spa" class="mr-2">
                                <span class="text-sm">Spa</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="amenity" value="restaurant" class="mr-2">
                                <span class="text-sm">Restaurant</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="amenity" value="parking" class="mr-2">
                                <span class="text-sm">Free Parking</span>
                            </label>
                        </div>
                    </div>

                    <!-- Property Type -->
                    <div class="mb-6">
                        <h4 class="font-medium mb-3">Property Type</h4>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" name="type" value="hotel" class="mr-2">
                                <span class="text-sm">Hotel</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="type" value="resort" class="mr-2">
                                <span class="text-sm">Resort</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="type" value="apartment" class="mr-2">
                                <span class="text-sm">Apartment</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="type" value="villa" class="mr-2">
                                <span class="text-sm">Villa</span>
                            </label>
                        </div>
                    </div>

                    <button type="button" id="apply-filters" class="btn btn-primary w-full">Apply Filters</button>
                    <button type="button" id="clear-filters" class="btn btn-secondary w-full mt-2">Clear All</button>
                </div>
            </aside>

            <!-- Main Content -->
            <main class="lg:w-3/4">
                <!-- Results Header -->
                <div class="flex justify-between items-center mb-6">
                    <div>
                        <h2 class="text-2xl font-bold" id="results-title">Available Hotels</h2>
                        <p class="text-gray-600" id="results-count">Loading...</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <label class="text-sm font-medium text-gray-700">Sort by:</label>
                        <select id="sort-by" class="input text-sm">
                            <option value="price-low">Price: Low to High</option>
                            <option value="price-high">Price: High to Low</option>
                            <option value="rating">Guest Rating</option>
                            <option value="distance">Distance</option>
                            <option value="popularity">Popularity</option>
                        </select>
                    </div>
                </div>

                <!-- Hotel List -->
                <div id="hotel-list" class="space-y-6">
                    <!-- Hotels will be loaded here -->
                    <div class="animate-pulse">
                        <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                            <div class="flex">
                                <div class="w-1/3 bg-gray-200 h-48"></div>
                                <div class="w-2/3 p-6">
                                    <div class="h-6 bg-gray-200 rounded mb-2"></div>
                                    <div class="h-4 bg-gray-200 rounded mb-2 w-3/4"></div>
                                    <div class="h-4 bg-gray-200 rounded mb-4 w-1/2"></div>
                                    <div class="h-8 bg-gray-200 rounded w-1/4"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Pagination -->
                <div id="pagination" class="flex justify-center mt-8">
                    <!-- Pagination will be loaded here -->
                </div>
            </main>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8 mt-12">
        <div class="container mx-auto px-4 text-center">
            <p>&copy; 2024 Hotel Marketplace. All rights reserved.</p>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="js/api.js"></script>
    <script src="js/utils.js"></script>
    <script>
        let currentFilters = {};
        let currentPage = 1;
        let totalPages = 1;

        document.addEventListener('DOMContentLoaded', function() {
            // Initialize mobile menu toggle
            const mobileMenuButton = document.getElementById('mobile-menu-button');
            const mobileMenu = document.getElementById('mobile-menu');

            if (mobileMenuButton && mobileMenu) {
                mobileMenuButton.addEventListener('click', function() {
                    mobileMenu.classList.toggle('hidden');
                });
            }

            // Update navigation based on authentication status
            utils.updateNavigation();

            // Set minimum dates for check-in and check-out
            const today = new Date();
            const tomorrow = new Date(today);
            tomorrow.setDate(tomorrow.getDate() + 1);

            const checkInInput = document.getElementById('check-in');
            const checkOutInput = document.getElementById('check-out');

            if (checkInInput && checkOutInput) {
                checkInInput.min = utils.formatDate(today);
                checkOutInput.min = utils.formatDate(tomorrow);

                // Set default values from URL parameters or today/tomorrow
                const urlParams = new URLSearchParams(window.location.search);
                checkInInput.value = urlParams.get('checkIn') || utils.formatDate(today);
                checkOutInput.value = urlParams.get('checkOut') || utils.formatDate(tomorrow);

                // Update check-out min date when check-in changes
                checkInInput.addEventListener('change', function() {
                    const newMinDate = new Date(this.value);
                    newMinDate.setDate(newMinDate.getDate() + 1);
                    checkOutInput.min = utils.formatDate(newMinDate);

                    if (new Date(checkOutInput.value) <= new Date(this.value)) {
                        checkOutInput.value = utils.formatDate(newMinDate);
                    }
                });
            }

            // Initialize destination input
            const destinationInput = document.getElementById('destination');
            if (destinationInput) {
                const urlParams = new URLSearchParams(window.location.search);
                destinationInput.value = urlParams.get('destination') || '';
            }

            // Handle main search form submission
            const mainSearchForm = document.getElementById('main-search-form');
            if (mainSearchForm) {
                mainSearchForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    performSearch();
                });
            }

            // Handle filter buttons
            document.getElementById('apply-filters').addEventListener('click', applyFilters);
            document.getElementById('clear-filters').addEventListener('click', clearFilters);

            // Handle sort change
            document.getElementById('sort-by').addEventListener('change', function() {
                currentFilters.sortBy = this.value;
                loadHotels();
            });

            // Load initial hotels
            loadHotelsFromURL();
        });

        function performSearch() {
            const destination = document.getElementById('destination').value;
            const checkIn = document.getElementById('check-in').value;
            const checkOut = document.getElementById('check-out').value;

            currentFilters = {
                destination: destination,
                checkIn: checkIn,
                checkOut: checkOut,
                page: 1
            };

            updateURL();
            loadHotels();
        }

        function applyFilters() {
            // Get price range
            const minPrice = document.getElementById('min-price').value;
            const maxPrice = document.getElementById('max-price').value;

            // Get selected ratings
            const ratings = Array.from(document.querySelectorAll('input[name="rating"]:checked')).map(cb => cb.value);

            // Get selected amenities
            const amenities = Array.from(document.querySelectorAll('input[name="amenity"]:checked')).map(cb => cb.value);

            // Get selected property types
            const types = Array.from(document.querySelectorAll('input[name="type"]:checked')).map(cb => cb.value);

            // Update current filters
            if (minPrice) currentFilters.minPrice = minPrice;
            if (maxPrice) currentFilters.maxPrice = maxPrice;
            if (ratings.length > 0) currentFilters.ratings = ratings;
            if (amenities.length > 0) currentFilters.amenities = amenities;
            if (types.length > 0) currentFilters.types = types;

            currentFilters.page = 1;
            currentPage = 1;

            updateURL();
            loadHotels();
        }

        function clearFilters() {
            // Clear all filter inputs
            document.getElementById('min-price').value = '';
            document.getElementById('max-price').value = '';

            document.querySelectorAll('input[name="rating"]').forEach(cb => cb.checked = false);
            document.querySelectorAll('input[name="amenity"]').forEach(cb => cb.checked = false);
            document.querySelectorAll('input[name="type"]').forEach(cb => cb.checked = false);

            // Reset filters
            currentFilters = {
                destination: currentFilters.destination || '',
                checkIn: currentFilters.checkIn || '',
                checkOut: currentFilters.checkOut || '',
                page: 1
            };

            currentPage = 1;
            updateURL();
            loadHotels();
        }

        function loadHotelsFromURL() {
            const urlParams = new URLSearchParams(window.location.search);
            currentFilters = {
                destination: urlParams.get('destination') || '',
                checkIn: urlParams.get('checkIn') || '',
                checkOut: urlParams.get('checkOut') || '',
                minPrice: urlParams.get('minPrice') || '',
                maxPrice: urlParams.get('maxPrice') || '',
                page: parseInt(urlParams.get('page')) || 1
            };

            currentPage = currentFilters.page;
            loadHotels();
        }

        function updateURL() {
            const params = new URLSearchParams();
            Object.keys(currentFilters).forEach(key => {
                if (currentFilters[key] && currentFilters[key] !== '') {
                    if (Array.isArray(currentFilters[key])) {
                        currentFilters[key].forEach(value => params.append(key, value));
                    } else {
                        params.set(key, currentFilters[key]);
                    }
                }
            });

            const newURL = `${window.location.pathname}?${params.toString()}`;
            window.history.pushState({}, '', newURL);
        }

        async function loadHotels() {
            const hotelList = document.getElementById('hotel-list');
            const resultsCount = document.getElementById('results-count');

            if (!hotelList) return;

            // Show loading state
            hotelList.innerHTML = Array(3).fill(`
                <div class="animate-pulse">
                    <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                        <div class="flex">
                            <div class="w-1/3 bg-gray-200 h-48"></div>
                            <div class="w-2/3 p-6">
                                <div class="h-6 bg-gray-200 rounded mb-2"></div>
                                <div class="h-4 bg-gray-200 rounded mb-2 w-3/4"></div>
                                <div class="h-4 bg-gray-200 rounded mb-4 w-1/2"></div>
                                <div class="h-8 bg-gray-200 rounded w-1/4"></div>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');

            try {
                // Call API to get hotels
                const response = await fetch(`http://localhost:5093/api/hotels?${buildQueryString()}`);
                const result = await response.json();

                if (result.success && result.data) {
                    const hotels = result.data;

                    if (hotels.length === 0) {
                        hotelList.innerHTML = `
                            <div class="col-span-full text-center py-12">
                                <div class="text-center">
                                    <i class="fas fa-search text-6xl text-gray-300 mb-4"></i>
                                    <h3 class="text-xl font-medium text-gray-900 mb-2">No hotels found</h3>
                                    <p class="text-gray-600">Try adjusting your search filters or search criteria</p>
                                </div>
                            </div>
                        `;
                        resultsCount.textContent = 'No results found';
                        return;
                    }

                    // Update results count
                    resultsCount.textContent = `${hotels.length} hotels found`;

                    // Render hotels
                    hotelList.innerHTML = hotels.map(hotel => `
                        <div class="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow duration-300">
                            <div class="flex flex-col md:flex-row">
                                <div class="md:w-1/3">
                                    <img src="${hotel.imageUrls && hotel.imageUrls.length > 0 ? hotel.imageUrls[0] : 'https://via.placeholder.com/300x200?text=Hotel+Image'}"
                                         alt="${hotel.name}"
                                         class="w-full h-48 md:h-full object-cover">
                                </div>
                                <div class="md:w-2/3 p-6">
                                    <div class="flex justify-between items-start mb-2">
                                        <h3 class="text-xl font-semibold text-gray-900">${hotel.name}</h3>
                                        <div class="flex items-center bg-primary-100 text-primary-800 px-2 py-1 rounded text-sm">
                                            <i class="fas fa-star mr-1"></i>
                                            <span>${hotel.rating ? hotel.rating.toFixed(1) : 'N/A'}</span>
                                        </div>
                                    </div>

                                    <p class="text-gray-600 mb-2">
                                        <i class="fas fa-map-marker-alt mr-1"></i>
                                        ${hotel.city}, ${hotel.state}, ${hotel.country}
                                    </p>

                                    <p class="text-gray-700 mb-4 line-clamp-2">${hotel.description}</p>

                                    ${hotel.amenities && hotel.amenities.length > 0 ? `
                                        <div class="flex flex-wrap gap-2 mb-4">
                                            ${hotel.amenities.slice(0, 3).map(amenity => `
                                                <span class="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs">${amenity}</span>
                                            `).join('')}
                                            ${hotel.amenities.length > 3 ? `<span class="text-gray-500 text-xs">+${hotel.amenities.length - 3} more</span>` : ''}
                                        </div>
                                    ` : ''}

                                    <div class="flex justify-between items-center">
                                        <div>
                                            <span class="text-2xl font-bold text-primary-600">$${hotel.startingPrice ? hotel.startingPrice.toFixed(2) : '0.00'}</span>
                                            <span class="text-gray-600 text-sm">/ night</span>
                                        </div>
                                        <a href="hotel-details.html?id=${hotel.id}" class="btn btn-primary">
                                            View Details
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `).join('');

                    // Update pagination if needed
                    updatePagination(result.totalPages || 1, result.currentPage || 1);

                } else {
                    throw new Error(result.message || 'Failed to load hotels');
                }

            } catch (error) {
                console.error('Error loading hotels:', error);
                hotelList.innerHTML = `
                    <div class="col-span-full text-center py-12">
                        <div class="text-center">
                            <i class="fas fa-exclamation-triangle text-6xl text-red-300 mb-4"></i>
                            <h3 class="text-xl font-medium text-gray-900 mb-2">Error loading hotels</h3>
                            <p class="text-gray-600">Please try again later</p>
                            <button onclick="loadHotels()" class="btn btn-primary mt-4">Retry</button>
                        </div>
                    </div>
                `;
                resultsCount.textContent = 'Error loading results';
                utils.showAlert('Error loading hotels. Please try again later.', 'error');
            }
        }

        function buildQueryString() {
            const params = new URLSearchParams();

            if (currentFilters.destination) params.set('search', currentFilters.destination);
            if (currentFilters.checkIn) params.set('checkIn', currentFilters.checkIn);
            if (currentFilters.checkOut) params.set('checkOut', currentFilters.checkOut);
            if (currentFilters.minPrice) params.set('minPrice', currentFilters.minPrice);
            if (currentFilters.maxPrice) params.set('maxPrice', currentFilters.maxPrice);
            if (currentFilters.sortBy) params.set('sortBy', currentFilters.sortBy);

            params.set('page', currentPage);
            params.set('pageSize', '10');

            return params.toString();
        }

        function updatePagination(totalPagesCount, currentPageNum) {
            totalPages = totalPagesCount;
            currentPage = currentPageNum;

            const pagination = document.getElementById('pagination');
            if (!pagination || totalPages <= 1) {
                pagination.innerHTML = '';
                return;
            }

            let paginationHTML = '';

            // Previous button
            if (currentPage > 1) {
                paginationHTML += `<button onclick="changePage(${currentPage - 1})" class="btn btn-secondary mr-2">Previous</button>`;
            }

            // Page numbers
            for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
                if (i === currentPage) {
                    paginationHTML += `<button class="btn btn-primary mr-2">${i}</button>`;
                } else {
                    paginationHTML += `<button onclick="changePage(${i})" class="btn btn-secondary mr-2">${i}</button>`;
                }
            }

            // Next button
            if (currentPage < totalPages) {
                paginationHTML += `<button onclick="changePage(${currentPage + 1})" class="btn btn-secondary">Next</button>`;
            }

            pagination.innerHTML = paginationHTML;
        }

        function changePage(page) {
            currentPage = page;
            currentFilters.page = page;
            updateURL();
            loadHotels();
        }
    </script>
</body>
</html>