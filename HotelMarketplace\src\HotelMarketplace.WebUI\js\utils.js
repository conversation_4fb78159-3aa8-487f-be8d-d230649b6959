// Utility functions for the application
window.utils = {
    // Check if user is logged in
    isLoggedIn: () => {
        return localStorage.getItem('token') !== null;
    },

    // Get current user data
    getCurrentUser: () => {
        return JSON.parse(localStorage.getItem('user') || 'null');
    },

    // Format date to YYYY-MM-DD
    formatDate: (date) => {
        const d = new Date(date);
        let month = '' + (d.getMonth() + 1);
        let day = '' + d.getDate();
        const year = d.getFullYear();

        if (month.length < 2) month = '0' + month;
        if (day.length < 2) day = '0' + day;

        return [year, month, day].join('-');
    },

    // Show alert message
    showAlert: (message, type = 'info', duration = 5000) => {
        const alertContainer = document.getElementById('alert-container');
        if (!alertContainer) return;

        const alertId = 'alert-' + Date.now();
        const alertColors = {
            success: 'bg-green-100 border-green-500 text-green-700',
            error: 'bg-red-100 border-red-500 text-red-700',
            warning: 'bg-yellow-100 border-yellow-500 text-yellow-700',
            info: 'bg-blue-100 border-blue-500 text-blue-700'
        };

        const alertHtml = `
            <div id="${alertId}" class="mb-4 p-4 rounded border-l-4 ${alertColors[type]} animate-fade-in">
                <div class="flex items-center justify-between">
                    <p>${message}</p>
                    <button class="text-gray-500 hover:text-gray-700 focus:outline-none alert-close">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                    </button>
                </div>
            </div>
        `;

        alertContainer.insertAdjacentHTML('afterbegin', alertHtml);

        const alertElement = document.getElementById(alertId);

        // Add close button event listener
        alertElement.querySelector('.alert-close').addEventListener('click', () => {
            alertElement.remove();
        });

        // Auto-remove after duration
        setTimeout(() => {
            if (alertElement && alertElement.parentNode) {
                alertElement.classList.add('animate-fade-out');
                setTimeout(() => alertElement.remove(), 300);
            }
        }, duration);
    },

    // Update navigation based on authentication status
    updateNavigation: () => {
        const authLinks = document.querySelectorAll('.auth-link');
        const userLinks = document.querySelectorAll('.user-link');
        const userNameElements = document.querySelectorAll('.user-name');

        if (utils.isLoggedIn()) {
            // Hide auth links, show user links
            authLinks.forEach(link => link.classList.add('hidden'));
            userLinks.forEach(link => link.classList.remove('hidden'));

            // Update user name
            const user = utils.getCurrentUser();
            if (user) {
                userNameElements.forEach(el => {
                    el.textContent = user.firstName || user.email;
                });
            }
        } else {
            // Show auth links, hide user links
            authLinks.forEach(link => link.classList.remove('hidden'));
            userLinks.forEach(link => link.classList.add('hidden'));
        }
    },

    // Logout user
    logout: () => {
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        window.location.href = 'index.html';
    },

    // Validate email format
    validateEmail: (email) => {
        const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
        return re.test(String(email).toLowerCase());
    },

    // Format currency
    formatCurrency: (amount) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(amount);
    },

    // Calculate number of nights between two dates
    calculateNights: (checkIn, checkOut) => {
        const start = new Date(checkIn);
        const end = new Date(checkOut);
        const diffTime = Math.abs(end - start);
        return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    },

    // Get user's dashboard URL based on role
    getDashboardUrl: () => {
        const user = utils.getCurrentUser();
        if (!user) return 'index.html';

        switch (user.role) {
            case 'Admin':
            case '0':
                return 'admin-dashboard.html';
            case 'HotelOwner':
            case '1':
                return 'hotel-owner-dashboard.html';
            case 'Customer':
            case '2':
            default:
                return 'customer-dashboard.html';
        }
    },

    // Get user role display name
    getRoleDisplayName: (role) => {
        switch (role) {
            case 'Admin':
            case '0':
                return 'Administrator';
            case 'HotelOwner':
            case '1':
                return 'Hotel Owner';
            case 'Customer':
            case '2':
            default:
                return 'Customer';
        }
    }
};

// Add CSS for animations
const style = document.createElement('style');
style.textContent = `
    .animate-fade-in {
        animation: fadeIn 0.3s ease-in-out;
    }

    .animate-fade-out {
        animation: fadeOut 0.3s ease-in-out;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    @keyframes fadeOut {
        from { opacity: 1; transform: translateY(0); }
        to { opacity: 0; transform: translateY(-10px); }
    }

    .input {
        width: 100%;
        padding: 0.5rem 0.75rem;
        border: 1px solid #d1d5db;
        border-radius: 0.375rem;
        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    }

    .input:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .btn {
        display: inline-block;
        padding: 0.5rem 1rem;
        font-weight: 500;
        text-align: center;
        border-radius: 0.375rem;
        transition: all 0.15s ease-in-out;
    }

    .btn-primary {
        background-color: #0ea5e9;
        color: white;
    }

    .btn-primary:hover {
        background-color: #0284c7;
    }

    .btn-secondary {
        background-color: #f3f4f6;
        color: #1f2937;
    }

    .btn-secondary:hover {
        background-color: #e5e7eb;
    }

    .card {
        background-color: white;
        border-radius: 0.5rem;
        overflow: hidden;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    }
`;
document.head.appendChild(style);

