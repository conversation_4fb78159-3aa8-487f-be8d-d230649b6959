// Booking confirmation page functionality
class BookingConfirmation {
    constructor() {
        this.bookingId = null;
        this.booking = null;
        this.init();
    }

    init() {
        this.bookingId = this.getBookingIdFromURL();
        if (!this.bookingId) {
            this.showError();
            return;
        }

        this.setupEventListeners();
        this.loadBookingDetails();
    }

    getBookingIdFromURL() {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get('id');
    }

    setupEventListeners() {
        // Download confirmation
        const downloadBtn = document.getElementById('download-confirmation');
        if (downloadBtn) {
            downloadBtn.addEventListener('click', () => this.downloadConfirmation());
        }

        // Email confirmation
        const emailBtn = document.getElementById('email-confirmation');
        if (emailBtn) {
            emailBtn.addEventListener('click', () => this.emailConfirmation());
        }

        // Modify booking
        const modifyBtn = document.getElementById('modify-booking');
        if (modifyBtn) {
            modifyBtn.addEventListener('click', () => this.modifyBooking());
        }

        // Cancel booking
        const cancelBtn = document.getElementById('cancel-booking');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => this.cancelBooking());
        }
    }

    async loadBookingDetails() {
        try {
            const response = await window.api.bookings.getBookingById(this.bookingId);
            
            if (response.success) {
                this.booking = response.data;
                this.renderBookingDetails();
                this.hideLoading();
            } else {
                this.showError();
            }
        } catch (error) {
            console.error('Error loading booking details:', error);
            this.showError();
        }
    }

    renderBookingDetails() {
        if (!this.booking) return;

        // Update page title
        document.title = `Booking Confirmation - ${this.booking.hotel.name} - Hotel Marketplace`;

        // Booking summary
        document.getElementById('booking-reference').textContent = this.booking.referenceNumber || this.booking.id;
        document.getElementById('booking-date').textContent = utils.formatDate(new Date(this.booking.createdAt));

        // Hotel information
        const hotelImage = document.getElementById('hotel-image');
        hotelImage.src = this.booking.hotel.imageUrl || 'images/hotel-placeholder.jpg';
        hotelImage.alt = this.booking.hotel.name;

        document.getElementById('hotel-name').textContent = this.booking.hotel.name;
        document.getElementById('hotel-location').innerHTML = `
            <i class="fas fa-map-marker-alt mr-2"></i>
            ${this.booking.hotel.location}
        `;

        // Hotel rating
        this.renderHotelRating(this.booking.hotel.rating);

        // Stay details
        document.getElementById('checkin-date').textContent = utils.formatDate(new Date(this.booking.checkIn));
        document.getElementById('checkout-date').textContent = utils.formatDate(new Date(this.booking.checkOut));
        
        const nights = this.calculateNights(this.booking.checkIn, this.booking.checkOut);
        document.getElementById('stay-duration').textContent = `${nights} night${nights !== 1 ? 's' : ''}`;
        document.getElementById('guest-count').textContent = `${this.booking.guests} guest${this.booking.guests !== 1 ? 's' : ''}`;

        // Room details
        this.renderRoomDetails();

        // Price breakdown
        this.renderPriceBreakdown();

        // Guest information
        this.renderGuestInformation();
    }

    renderHotelRating(rating) {
        const ratingContainer = document.getElementById('hotel-rating');
        if (!rating) {
            ratingContainer.innerHTML = '<span class="text-gray-500">No rating</span>';
            return;
        }

        const fullStars = Math.floor(rating);
        const hasHalfStar = rating % 1 >= 0.5;
        const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

        let starsHTML = '';
        
        // Full stars
        for (let i = 0; i < fullStars; i++) {
            starsHTML += '<i class="fas fa-star text-yellow-400"></i>';
        }
        
        // Half star
        if (hasHalfStar) {
            starsHTML += '<i class="fas fa-star-half-alt text-yellow-400"></i>';
        }
        
        // Empty stars
        for (let i = 0; i < emptyStars; i++) {
            starsHTML += '<i class="far fa-star text-yellow-400"></i>';
        }

        ratingContainer.innerHTML = `
            <div class="flex items-center">
                ${starsHTML}
                <span class="ml-2 text-sm text-gray-600">(${rating.toFixed(1)})</span>
            </div>
        `;
    }

    renderRoomDetails() {
        const roomContainer = document.getElementById('room-details');
        if (!this.booking.room) {
            roomContainer.innerHTML = '<p class="text-gray-600">Room details not available</p>';
            return;
        }

        const room = this.booking.room;
        roomContainer.innerHTML = `
            <div class="border rounded-lg p-4">
                <h4 class="font-semibold text-lg mb-2">${room.type}</h4>
                <p class="text-gray-600 mb-3">${room.description || 'Comfortable room with modern amenities'}</p>
                
                <div class="grid grid-cols-2 gap-4 text-sm">
                    <div>
                        <span class="text-gray-600">Bed Type:</span>
                        <span class="font-medium ml-2">${room.bedType || 'Standard'}</span>
                    </div>
                    <div>
                        <span class="text-gray-600">Max Guests:</span>
                        <span class="font-medium ml-2">${room.capacity}</span>
                    </div>
                    <div>
                        <span class="text-gray-600">Size:</span>
                        <span class="font-medium ml-2">${room.size || 'N/A'} sq ft</span>
                    </div>
                    <div>
                        <span class="text-gray-600">View:</span>
                        <span class="font-medium ml-2">${room.view || 'Standard'}</span>
                    </div>
                </div>

                ${room.amenities && room.amenities.length > 0 ? `
                    <div class="mt-3">
                        <span class="text-gray-600 text-sm">Amenities:</span>
                        <div class="flex flex-wrap gap-1 mt-1">
                            ${room.amenities.map(amenity => `
                                <span class="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded">${amenity}</span>
                            `).join('')}
                        </div>
                    </div>
                ` : ''}
            </div>
        `;
    }

    renderPriceBreakdown() {
        const priceContainer = document.getElementById('price-breakdown');
        if (!this.booking.pricing) {
            priceContainer.innerHTML = '<p class="text-gray-600">Price details not available</p>';
            return;
        }

        const pricing = this.booking.pricing;
        const nights = this.calculateNights(this.booking.checkIn, this.booking.checkOut);

        priceContainer.innerHTML = `
            <div class="space-y-3">
                <div class="flex justify-between">
                    <span class="text-gray-600">Room rate (${nights} night${nights !== 1 ? 's' : ''})</span>
                    <span>$${(pricing.roomRate * nights).toFixed(2)}</span>
                </div>
                
                ${pricing.taxes ? `
                    <div class="flex justify-between">
                        <span class="text-gray-600">Taxes & fees</span>
                        <span>$${pricing.taxes.toFixed(2)}</span>
                    </div>
                ` : ''}
                
                ${pricing.discount ? `
                    <div class="flex justify-between text-green-600">
                        <span>Discount</span>
                        <span>-$${pricing.discount.toFixed(2)}</span>
                    </div>
                ` : ''}
                
                <div class="border-t pt-3 flex justify-between font-semibold text-lg">
                    <span>Total</span>
                    <span>$${pricing.total.toFixed(2)}</span>
                </div>
                
                <div class="text-xs text-gray-500">
                    Includes all taxes and fees
                </div>
            </div>
        `;
    }

    renderGuestInformation() {
        const guestContainer = document.getElementById('guest-info');
        if (!this.booking.guestInfo) {
            guestContainer.innerHTML = '<p class="text-gray-600">Guest information not available</p>';
            return;
        }

        const guest = this.booking.guestInfo;
        guestContainer.innerHTML = `
            <div class="space-y-3">
                <div>
                    <span class="text-gray-600 text-sm">Primary Guest:</span>
                    <p class="font-medium">${guest.firstName} ${guest.lastName}</p>
                </div>
                
                <div>
                    <span class="text-gray-600 text-sm">Email:</span>
                    <p class="font-medium">${guest.email}</p>
                </div>
                
                ${guest.phone ? `
                    <div>
                        <span class="text-gray-600 text-sm">Phone:</span>
                        <p class="font-medium">${guest.phone}</p>
                    </div>
                ` : ''}
                
                ${guest.specialRequests ? `
                    <div>
                        <span class="text-gray-600 text-sm">Special Requests:</span>
                        <p class="text-sm">${guest.specialRequests}</p>
                    </div>
                ` : ''}
            </div>
        `;
    }

    calculateNights(checkIn, checkOut) {
        const checkinDate = new Date(checkIn);
        const checkoutDate = new Date(checkOut);
        const timeDiff = checkoutDate.getTime() - checkinDate.getTime();
        return Math.ceil(timeDiff / (1000 * 3600 * 24));
    }

    async downloadConfirmation() {
        try {
            utils.showAlert('Generating PDF...', 'info');
            
            // This would typically call an API to generate a PDF
            // For now, we'll simulate the download
            setTimeout(() => {
                utils.showAlert('PDF download started', 'success');
            }, 1000);
        } catch (error) {
            console.error('Error downloading confirmation:', error);
            utils.showAlert('Error downloading confirmation', 'error');
        }
    }

    async emailConfirmation() {
        try {
            const response = await window.api.bookings.emailConfirmation(this.bookingId);
            
            if (response.success) {
                utils.showAlert('Confirmation email sent successfully', 'success');
            } else {
                utils.showAlert(response.message || 'Failed to send email', 'error');
            }
        } catch (error) {
            console.error('Error sending email:', error);
            utils.showAlert('Error sending confirmation email', 'error');
        }
    }

    modifyBooking() {
        // Redirect to booking modification page
        window.location.href = `modify-booking.html?id=${this.bookingId}`;
    }

    async cancelBooking() {
        const confirmed = confirm('Are you sure you want to cancel this booking? This action cannot be undone.');
        if (!confirmed) return;

        try {
            const response = await window.api.bookings.cancelBooking(this.bookingId);
            
            if (response.success) {
                utils.showAlert('Booking cancelled successfully', 'success');
                setTimeout(() => {
                    window.location.href = 'bookings.html';
                }, 2000);
            } else {
                utils.showAlert(response.message || 'Failed to cancel booking', 'error');
            }
        } catch (error) {
            console.error('Error cancelling booking:', error);
            utils.showAlert('Error cancelling booking', 'error');
        }
    }

    hideLoading() {
        document.getElementById('loading-state').classList.add('hidden');
        document.getElementById('booking-content').classList.remove('hidden');
    }

    showError() {
        document.getElementById('loading-state').classList.add('hidden');
        document.getElementById('error-state').classList.remove('hidden');
    }
}

// Initialize booking confirmation when DOM is loaded
let bookingConfirmation;
document.addEventListener('DOMContentLoaded', () => {
    bookingConfirmation = new BookingConfirmation();
    
    // Update navigation
    if (window.utils) {
        utils.updateNavigation();
    }
});
