using HotelMarketplace.Application.Services;
using HotelMarketplace.Core.DTOs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using System.Threading.Tasks;

namespace HotelMarketplace.WebAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize(Roles = "Admin")]
    public class AdminController : ControllerBase
    {
        private readonly IAdminService _adminService;
        private readonly IHotelService _hotelService;
        private readonly IBookingService _bookingService;
        private readonly IReviewService _reviewService;
        private readonly IAuthService _authService;

        public AdminController(
            IAdminService adminService,
            IHotelService hotelService,
            IBookingService bookingService,
            IReviewService reviewService,
            IAuthService authService)
        {
            _adminService = adminService;
            _hotelService = hotelService;
            _bookingService = bookingService;
            _reviewService = reviewService;
            _authService = authService;
        }

        [HttpGet("dashboard")]
        public async Task<IActionResult> GetDashboardStats()
        {
            var stats = await _adminService.GetDashboardStatsAsync();
            return Ok(stats);
        }

        [HttpGet("users")]
        public async Task<IActionResult> GetAllUsers([FromQuery] int page = 1, [FromQuery] int pageSize = 10)
        {
            var users = await _adminService.GetAllUsersAsync(page, pageSize);
            return Ok(users);
        }

        [HttpGet("users/{userId}")]
        public async Task<IActionResult> GetUserById(string userId)
        {
            var user = await _adminService.GetUserByIdAsync(userId);
            if (!user.Success)
                return NotFound(user);

            return Ok(user);
        }

        [HttpPut("users/{userId}/status")]
        public async Task<IActionResult> UpdateUserStatus(string userId, [FromBody] UserStatusUpdateDto statusDto)
        {
            var result = await _adminService.UpdateUserStatusAsync(userId, statusDto);
            if (!result.Success)
                return BadRequest(result);

            return Ok(result);
        }

        [HttpGet("hotels/pending")]
        public async Task<IActionResult> GetPendingHotels()
        {
            var hotels = await _adminService.GetPendingHotelsAsync();
            return Ok(hotels);
        }

        [HttpPut("hotels/{hotelId}/approve")]
        public async Task<IActionResult> ApproveHotel(int hotelId)
        {
            var adminId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var result = await _adminService.ApproveHotelAsync(hotelId, adminId);
            if (!result.Success)
                return BadRequest(result);

            return Ok(result);
        }

        [HttpPut("hotels/{hotelId}/reject")]
        public async Task<IActionResult> RejectHotel(int hotelId, [FromBody] HotelRejectionDto rejectionDto)
        {
            var adminId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var result = await _adminService.RejectHotelAsync(hotelId, adminId, rejectionDto.Reason);
            if (!result.Success)
                return BadRequest(result);

            return Ok(result);
        }

        [HttpGet("reviews/pending")]
        public async Task<IActionResult> GetPendingReviews()
        {
            var reviews = await _adminService.GetPendingReviewsAsync();
            return Ok(reviews);
        }

        [HttpGet("bookings")]
        public async Task<IActionResult> GetAllBookings([FromQuery] int page = 1, [FromQuery] int pageSize = 10)
        {
            var bookings = await _adminService.GetAllBookingsAsync(page, pageSize);
            return Ok(bookings);
        }

        [HttpGet("reports/revenue")]
        public async Task<IActionResult> GetRevenueReport([FromQuery] DateTime? startDate, [FromQuery] DateTime? endDate)
        {
            var report = await _adminService.GetRevenueReportAsync(startDate, endDate);
            return Ok(report);
        }

        [HttpGet("reports/bookings")]
        public async Task<IActionResult> GetBookingReport([FromQuery] DateTime? startDate, [FromQuery] DateTime? endDate)
        {
            var report = await _adminService.GetBookingReportAsync(startDate, endDate);
            return Ok(report);
        }

        [HttpGet("reports/hotels")]
        public async Task<IActionResult> GetHotelReport()
        {
            var report = await _adminService.GetHotelReportAsync();
            return Ok(report);
        }

        [HttpPost("notifications/broadcast")]
        public async Task<IActionResult> BroadcastNotification([FromBody] BroadcastNotificationDto notificationDto)
        {
            var adminId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var result = await _adminService.BroadcastNotificationAsync(adminId, notificationDto);
            if (!result.Success)
                return BadRequest(result);

            return Ok(result);
        }

        [HttpGet("system/health")]
        public async Task<IActionResult> GetSystemHealth()
        {
            var health = await _adminService.GetSystemHealthAsync();
            return Ok(health);
        }

        [HttpPost("system/backup")]
        public async Task<IActionResult> CreateSystemBackup()
        {
            var adminId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var result = await _adminService.CreateSystemBackupAsync(adminId);
            if (!result.Success)
                return BadRequest(result);

            return Ok(result);
        }

        [HttpGet("analytics/overview")]
        public async Task<IActionResult> GetAnalyticsOverview([FromQuery] int days = 30)
        {
            var analytics = await _adminService.GetAnalyticsOverviewAsync(days);
            return Ok(analytics);
        }
    }
}
