using HotelMarketplace.Application.Services;
using HotelMarketplace.Core.DTOs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using System.Threading.Tasks;

namespace HotelMarketplace.WebAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class NotificationsController : ControllerBase
    {
        private readonly INotificationService _notificationService;

        public NotificationsController(INotificationService notificationService)
        {
            _notificationService = notificationService;
        }

        [HttpGet]
        public async Task<IActionResult> GetNotifications([FromQuery] int page = 1, [FromQuery] int pageSize = 20, [FromQuery] bool unreadOnly = false)
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var result = await _notificationService.GetUserNotificationsAsync(userId!, page, pageSize, unreadOnly);
            return Ok(result);
        }

        [HttpGet("unread-count")]
        public async Task<IActionResult> GetUnreadCount()
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var result = await _notificationService.GetUnreadCountAsync(userId!);
            return Ok(result);
        }

        [HttpPut("{notificationId}/read")]
        public async Task<IActionResult> MarkAsRead(int notificationId)
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var result = await _notificationService.MarkAsReadAsync(notificationId, userId!);
            if (!result.Success)
                return BadRequest(result);

            return Ok(result);
        }

        [HttpPut("mark-all-read")]
        public async Task<IActionResult> MarkAllAsRead()
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var result = await _notificationService.MarkAllAsReadAsync(userId!);
            if (!result.Success)
                return BadRequest(result);

            return Ok(result);
        }

        [HttpDelete("{notificationId}")]
        public async Task<IActionResult> DeleteNotification(int notificationId)
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var result = await _notificationService.DeleteNotificationAsync(notificationId, userId!);
            if (!result.Success)
                return BadRequest(result);

            return Ok(result);
        }

        [HttpDelete("clear-all")]
        public async Task<IActionResult> ClearAllNotifications()
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var result = await _notificationService.ClearAllNotificationsAsync(userId!);
            if (!result.Success)
                return BadRequest(result);

            return Ok(result);
        }

        [HttpGet("preferences")]
        public async Task<IActionResult> GetNotificationPreferences()
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var result = await _notificationService.GetNotificationPreferencesAsync(userId!);
            return Ok(result);
        }

        [HttpPut("preferences")]
        public async Task<IActionResult> UpdateNotificationPreferences([FromBody] NotificationPreferencesDto preferences)
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var result = await _notificationService.UpdateNotificationPreferencesAsync(userId!, preferences);
            if (!result.Success)
                return BadRequest(result);

            return Ok(result);
        }

        [HttpPost("test")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> SendTestNotification([FromBody] TestNotificationDto testNotification)
        {
            var result = await _notificationService.SendTestNotificationAsync(testNotification);
            if (!result.Success)
                return BadRequest(result);

            return Ok(result);
        }

        [HttpGet("templates")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> GetNotificationTemplates()
        {
            var result = await _notificationService.GetNotificationTemplatesAsync();
            return Ok(result);
        }

        [HttpPut("templates/{templateId}")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> UpdateNotificationTemplate(int templateId, [FromBody] NotificationTemplateDto template)
        {
            var result = await _notificationService.UpdateNotificationTemplateAsync(templateId, template);
            if (!result.Success)
                return BadRequest(result);

            return Ok(result);
        }
    }
}
