<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Login - Hotel Marketplace</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ccc; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { margin: 10px 0; padding: 10px; background: #f8f9fa; border-left: 4px solid #007bff; }
        .success { border-left-color: #28a745; background: #d4edda; }
        .error { border-left-color: #dc3545; background: #f8d7da; }
        input { padding: 8px; margin: 5px; width: 200px; }
    </style>
</head>
<body>
    <h1>🔐 Login Test Page</h1>
    <p>Test the login functionality with SQL Server database</p>

    <div class="test-section">
        <h3>📊 Database Connection Test</h3>
        <button onclick="testConnection()">Test API Connection</button>
        <div id="connection-result" class="result" style="display:none;"></div>
    </div>

    <div class="test-section">
        <h3>👤 Test Login with Seeded Users</h3>
        
        <h4>Admin User:</h4>
        <button onclick="testLogin('<EMAIL>', 'admin123')">Login as Admin</button>
        
        <h4>Customer User:</h4>
        <button onclick="testLogin('<EMAIL>', 'user123')">Login as Customer</button>
        
        <h4>Hotel Owner:</h4>
        <button onclick="testLogin('<EMAIL>', 'owner123')">Login as Hotel Owner</button>
        
        <div id="login-result" class="result" style="display:none;"></div>
    </div>

    <div class="test-section">
        <h3>🔑 Custom Login Test</h3>
        <input type="email" id="email" placeholder="Email" value="<EMAIL>">
        <input type="password" id="password" placeholder="Password" value="admin123">
        <button onclick="testCustomLogin()">Test Login</button>
        <div id="custom-result" class="result" style="display:none;"></div>
    </div>

    <div class="test-section">
        <h3>📝 Registration Test</h3>
        <button onclick="testRegistration()">Test Registration</button>
        <div id="register-result" class="result" style="display:none;"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5093/api';

        function showResult(elementId, message, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.innerHTML = message;
            element.className = `result ${isSuccess ? 'success' : 'error'}`;
            element.style.display = 'block';
        }

        async function testConnection() {
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'OPTIONS'
                });
                showResult('connection-result', '✅ API server is online and responding!', true);
            } catch (error) {
                showResult('connection-result', `❌ API server connection failed: ${error.message}`, false);
            }
        }

        async function testLogin(email, password) {
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email, password })
                });

                const result = await response.json();
                
                if (result.success) {
                    showResult('login-result', `✅ Login successful for ${email}!<br>Token: ${result.data.substring(0, 50)}...`, true);
                } else {
                    showResult('login-result', `❌ Login failed for ${email}: ${result.message}`, false);
                }
            } catch (error) {
                showResult('login-result', `❌ Login error: ${error.message}`, false);
            }
        }

        async function testCustomLogin() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            if (!email || !password) {
                showResult('custom-result', '❌ Please enter both email and password', false);
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email, password })
                });

                const result = await response.json();
                
                if (result.success) {
                    showResult('custom-result', `✅ Login successful for ${email}!<br>Token: ${result.data.substring(0, 50)}...`, true);
                } else {
                    showResult('custom-result', `❌ Login failed: ${result.message}`, false);
                }
            } catch (error) {
                showResult('custom-result', `❌ Login error: ${error.message}`, false);
            }
        }

        async function testRegistration() {
            const testUser = {
                firstName: 'Test',
                lastName: 'User',
                email: `test${Date.now()}@example.com`,
                phoneNumber: '+1234567890',
                password: 'test123',
                role: 2 // Customer
            };

            try {
                const response = await fetch(`${API_BASE}/auth/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testUser)
                });

                const result = await response.json();
                
                if (result.success) {
                    showResult('register-result', `✅ Registration successful for ${testUser.email}!<br>Token: ${result.data.substring(0, 50)}...`, true);
                } else {
                    showResult('register-result', `❌ Registration failed: ${result.message}`, false);
                }
            } catch (error) {
                showResult('register-result', `❌ Registration error: ${error.message}`, false);
            }
        }

        // Auto-test connection on page load
        window.addEventListener('load', testConnection);
    </script>
</body>
</html>
