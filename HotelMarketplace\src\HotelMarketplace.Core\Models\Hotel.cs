using System;
using System.Collections.Generic;

namespace HotelMarketplace.Core.Models
{
    public enum HotelStatus
    {
        Pending,
        Active,
        Rejected,
        Suspended
    }
    public class Hotel
    {
        public int Id { get; set; }
        public string OwnerId { get; set; }
        public string Name { get; set; }
        public string Location { get; set; }
        public string Description { get; set; }
        public double Rating { get; set; }
        public HotelStatus Status { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }

        // Navigation properties
        public User Owner { get; set; }
        public ICollection<Room> Rooms { get; set; }
        public ICollection<Review> Reviews { get; set; }
        public ICollection<HotelImage> Images { get; set; }

        public Hotel()
        {
            Rooms = new List<Room>();
            Reviews = new List<Review>();
            Images = new List<HotelImage>();
            CreatedAt = DateTime.UtcNow;
            Rating = 0;
        }
    }
}
