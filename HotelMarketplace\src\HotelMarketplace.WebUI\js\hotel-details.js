// Hotel details page functionality
class HotelDetailsManager {
    constructor() {
        this.hotelId = null;
        this.hotel = null;
        this.rooms = [];
        this.reviews = [];
        this.selectedRoom = null;
        
        this.init();
    }

    init() {
        this.hotelId = this.getHotelIdFromURL();
        if (!this.hotelId) {
            this.showError('Hotel not found');
            return;
        }

        this.setupEventListeners();
        this.loadHotelDetails();
        this.setupDateInputs();
    }

    getHotelIdFromURL() {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get('id');
    }

    setupEventListeners() {
        // Booking form
        const bookingForm = document.getElementById('booking-form');
        if (bookingForm) {
            bookingForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleBooking();
            });
        }

        // Date inputs for price calculation
        const checkinInput = document.getElementById('booking-checkin');
        const checkoutInput = document.getElementById('booking-checkout');
        const roomTypeSelect = document.getElementById('booking-room-type');

        if (checkinInput && checkoutInput && roomTypeSelect) {
            checkinInput.addEventListener('change', () => this.calculatePrice());
            checkoutInput.addEventListener('change', () => this.calculatePrice());
            roomTypeSelect.addEventListener('change', () => this.calculatePrice());
        }

        // Image modal
        const imageModal = document.getElementById('image-modal');
        const closeModal = document.getElementById('close-modal');
        
        if (imageModal && closeModal) {
            closeModal.addEventListener('click', () => {
                imageModal.classList.add('hidden');
            });
            
            imageModal.addEventListener('click', (e) => {
                if (e.target === imageModal) {
                    imageModal.classList.add('hidden');
                }
            });
        }

        // Write review button
        const writeReviewBtn = document.getElementById('write-review-btn');
        if (writeReviewBtn) {
            writeReviewBtn.addEventListener('click', () => this.showReviewForm());
        }
    }

    setupDateInputs() {
        const today = new Date();
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);

        const checkinInput = document.getElementById('booking-checkin');
        const checkoutInput = document.getElementById('booking-checkout');

        if (checkinInput && checkoutInput) {
            checkinInput.min = utils.formatDate(today);
            checkoutInput.min = utils.formatDate(tomorrow);

            // Set default values from URL params or today/tomorrow
            const urlParams = new URLSearchParams(window.location.search);
            checkinInput.value = urlParams.get('checkIn') || utils.formatDate(today);
            checkoutInput.value = urlParams.get('checkOut') || utils.formatDate(tomorrow);

            // Update checkout min date when checkin changes
            checkinInput.addEventListener('change', () => {
                const checkinDate = new Date(checkinInput.value);
                const nextDay = new Date(checkinDate);
                nextDay.setDate(nextDay.getDate() + 1);
                
                checkoutInput.min = utils.formatDate(nextDay);
                
                if (new Date(checkoutInput.value) <= checkinDate) {
                    checkoutInput.value = utils.formatDate(nextDay);
                }
                
                this.calculatePrice();
            });
        }
    }

    async loadHotelDetails() {
        try {
            const response = await window.api.hotels.getHotelById(this.hotelId);
            
            if (response.success) {
                this.hotel = response.data;
                this.renderHotelDetails();
                await this.loadRooms();
                await this.loadReviews();
                this.hideLoading();
            } else {
                this.showError('Failed to load hotel details');
            }
        } catch (error) {
            console.error('Error loading hotel details:', error);
            this.showError('Error loading hotel details');
        }
    }

    async loadRooms() {
        try {
            const response = await window.api.rooms.getRoomsByHotelId(this.hotelId);
            
            if (response.success) {
                this.rooms = response.data;
                this.renderRooms();
                this.populateRoomTypeSelect();
            }
        } catch (error) {
            console.error('Error loading rooms:', error);
        }
    }

    async loadReviews() {
        try {
            const response = await window.api.reviews.getReviewsByHotelId(this.hotelId);
            
            if (response.success) {
                this.reviews = response.data;
                this.renderReviews();
            }
        } catch (error) {
            console.error('Error loading reviews:', error);
        }
    }

    renderHotelDetails() {
        if (!this.hotel) return;

        // Update breadcrumb
        document.getElementById('hotel-breadcrumb').textContent = this.hotel.name;

        // Update page title
        document.title = `${this.hotel.name} - Hotel Marketplace`;

        // Hotel name and location
        document.getElementById('hotel-name').textContent = this.hotel.name;
        document.getElementById('hotel-location').innerHTML = `
            <i class="fas fa-map-marker-alt mr-2"></i>
            ${this.hotel.location}
        `;

        // Rating
        const ratingContainer = document.getElementById('hotel-rating');
        ratingContainer.innerHTML = this.renderStarRating(this.hotel.rating);

        // Price
        document.getElementById('hotel-price').textContent = `From $${this.hotel.lowestPrice?.toFixed(2) || '0.00'}/night`;

        // Description
        document.getElementById('hotel-description').textContent = this.hotel.description || 'No description available.';

        // Amenities
        this.renderAmenities();

        // Image gallery
        this.renderImageGallery();
    }

    renderStarRating(rating) {
        const fullStars = Math.floor(rating);
        const hasHalfStar = rating % 1 >= 0.5;
        const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

        let starsHTML = '';
        
        // Full stars
        for (let i = 0; i < fullStars; i++) {
            starsHTML += '<i class="fas fa-star text-yellow-400"></i>';
        }
        
        // Half star
        if (hasHalfStar) {
            starsHTML += '<i class="fas fa-star-half-alt text-yellow-400"></i>';
        }
        
        // Empty stars
        for (let i = 0; i < emptyStars; i++) {
            starsHTML += '<i class="far fa-star text-yellow-400"></i>';
        }

        return `
            <div class="flex items-center">
                ${starsHTML}
                <span class="ml-2 text-sm text-gray-600">(${rating.toFixed(1)})</span>
            </div>
        `;
    }

    renderAmenities() {
        const amenitiesContainer = document.getElementById('hotel-amenities');
        if (!this.hotel.amenities || this.hotel.amenities.length === 0) {
            amenitiesContainer.innerHTML = '<p class="text-gray-600">No amenities listed</p>';
            return;
        }

        const amenityIcons = {
            'wifi': 'fas fa-wifi',
            'parking': 'fas fa-parking',
            'pool': 'fas fa-swimming-pool',
            'gym': 'fas fa-dumbbell',
            'spa': 'fas fa-spa',
            'restaurant': 'fas fa-utensils',
            'bar': 'fas fa-cocktail',
            'room-service': 'fas fa-concierge-bell',
            'laundry': 'fas fa-tshirt',
            'business-center': 'fas fa-briefcase'
        };

        const amenitiesHTML = this.hotel.amenities.map(amenity => {
            const icon = amenityIcons[amenity.toLowerCase()] || 'fas fa-check';
            return `
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-primary-100 text-primary-800">
                    <i class="${icon} mr-2"></i>
                    ${amenity}
                </span>
            `;
        }).join('');

        amenitiesContainer.innerHTML = amenitiesHTML;
    }

    renderImageGallery() {
        const gallery = document.getElementById('image-gallery');
        const images = this.hotel.images || [this.hotel.imageUrl || 'images/hotel-placeholder.jpg'];

        if (images.length === 0) {
            gallery.innerHTML = '<img src="images/hotel-placeholder.jpg" alt="Hotel" class="w-full h-full object-cover">';
            return;
        }

        let galleryHTML = '';
        
        // Main image
        galleryHTML += `
            <img src="${images[0]}" alt="${this.hotel.name}" 
                 class="main-image gallery-image w-full h-full object-cover rounded-l-lg"
                 onclick="hotelDetailsManager.openImageModal('${images[0]}')">
        `;

        // Additional images
        for (let i = 1; i < Math.min(images.length, 5); i++) {
            galleryHTML += `
                <img src="${images[i]}" alt="${this.hotel.name}" 
                     class="gallery-image w-full h-full object-cover ${i === 4 ? 'rounded-br-lg' : ''}"
                     onclick="hotelDetailsManager.openImageModal('${images[i]}')">
            `;
        }

        // Show more overlay if there are more than 5 images
        if (images.length > 5) {
            galleryHTML += `
                <div class="relative gallery-image cursor-pointer rounded-br-lg overflow-hidden"
                     onclick="hotelDetailsManager.showAllImages()">
                    <img src="${images[4]}" alt="${this.hotel.name}" class="w-full h-full object-cover">
                    <div class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                        <span class="text-white font-semibold">+${images.length - 4} more</span>
                    </div>
                </div>
            `;
        }

        gallery.innerHTML = galleryHTML;
    }

    renderRooms() {
        const roomsList = document.getElementById('rooms-list');
        
        if (this.rooms.length === 0) {
            roomsList.innerHTML = '<p class="text-gray-600">No rooms available</p>';
            return;
        }

        const roomsHTML = this.rooms.map(room => `
            <div class="border rounded-lg p-4 mb-4">
                <div class="flex justify-between items-start">
                    <div class="flex-1">
                        <h3 class="text-lg font-semibold">${room.type}</h3>
                        <p class="text-gray-600 mb-2">${room.description || 'Comfortable room with modern amenities'}</p>
                        <div class="flex items-center text-sm text-gray-600 mb-2">
                            <i class="fas fa-users mr-2"></i>
                            <span>Max ${room.capacity} guests</span>
                            <i class="fas fa-bed ml-4 mr-2"></i>
                            <span>${room.bedType || 'Standard bed'}</span>
                        </div>
                        ${room.amenities ? `
                            <div class="flex flex-wrap gap-1 mb-2">
                                ${room.amenities.map(amenity => `
                                    <span class="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded">${amenity}</span>
                                `).join('')}
                            </div>
                        ` : ''}
                    </div>
                    <div class="text-right ml-4">
                        <div class="text-2xl font-bold text-primary-600">$${room.pricePerNight.toFixed(2)}</div>
                        <div class="text-sm text-gray-600">per night</div>
                        <button onclick="hotelDetailsManager.selectRoom('${room.id}')" 
                                class="btn btn-outline mt-2">
                            Select Room
                        </button>
                    </div>
                </div>
            </div>
        `).join('');

        roomsList.innerHTML = roomsHTML;
    }

    renderReviews() {
        const reviewsList = document.getElementById('reviews-list');
        
        if (this.reviews.length === 0) {
            reviewsList.innerHTML = '<p class="text-gray-600">No reviews yet. Be the first to review!</p>';
            return;
        }

        const reviewsHTML = this.reviews.map(review => `
            <div class="border-b pb-4 mb-4 last:border-b-0">
                <div class="flex justify-between items-start mb-2">
                    <div>
                        <h4 class="font-semibold">${review.userName}</h4>
                        <div class="flex items-center">
                            ${this.renderStarRating(review.rating)}
                            <span class="ml-2 text-sm text-gray-600">${utils.formatDate(new Date(review.createdAt))}</span>
                        </div>
                    </div>
                </div>
                <p class="text-gray-700">${review.comment}</p>
            </div>
        `).join('');

        reviewsList.innerHTML = reviewsHTML;
    }

    populateRoomTypeSelect() {
        const select = document.getElementById('booking-room-type');
        if (!select) return;

        select.innerHTML = '<option value="">Select Room Type</option>';
        
        this.rooms.forEach(room => {
            const option = document.createElement('option');
            option.value = room.id;
            option.textContent = `${room.type} - $${room.pricePerNight.toFixed(2)}/night`;
            select.appendChild(option);
        });
    }

    selectRoom(roomId) {
        this.selectedRoom = this.rooms.find(room => room.id === roomId);
        if (this.selectedRoom) {
            document.getElementById('booking-room-type').value = roomId;
            this.calculatePrice();
        }
    }

    calculatePrice() {
        const checkinDate = new Date(document.getElementById('booking-checkin').value);
        const checkoutDate = new Date(document.getElementById('booking-checkout').value);
        const roomTypeSelect = document.getElementById('booking-room-type');
        
        if (!checkinDate || !checkoutDate || !roomTypeSelect.value) {
            document.getElementById('price-breakdown').classList.add('hidden');
            return;
        }

        const selectedRoom = this.rooms.find(room => room.id === roomTypeSelect.value);
        if (!selectedRoom) return;

        const nights = Math.ceil((checkoutDate - checkinDate) / (1000 * 60 * 60 * 24));
        if (nights <= 0) return;

        const roomRate = selectedRoom.pricePerNight;
        const subtotal = roomRate * nights;
        const taxesAndFees = subtotal * 0.15; // 15% taxes and fees
        const total = subtotal + taxesAndFees;

        // Update price breakdown
        document.getElementById('room-rate').textContent = `$${roomRate.toFixed(2)}`;
        document.getElementById('nights-count').textContent = nights;
        document.getElementById('taxes-fees').textContent = `$${taxesAndFees.toFixed(2)}`;
        document.getElementById('total-price').textContent = `$${total.toFixed(2)}`;
        
        document.getElementById('price-breakdown').classList.remove('hidden');
    }

    async handleBooking() {
        if (!utils.isLoggedIn()) {
            utils.showAlert('Please log in to make a booking', 'warning');
            setTimeout(() => {
                window.location.href = 'login.html';
            }, 2000);
            return;
        }

        const formData = {
            hotelId: this.hotelId,
            roomId: document.getElementById('booking-room-type').value,
            checkIn: document.getElementById('booking-checkin').value,
            checkOut: document.getElementById('booking-checkout').value,
            guests: document.getElementById('booking-guests').value
        };

        if (!formData.roomId || !formData.checkIn || !formData.checkOut) {
            utils.showAlert('Please fill in all required fields', 'error');
            return;
        }

        try {
            const response = await window.api.bookings.createBooking(formData);
            
            if (response.success) {
                utils.showAlert('Booking created successfully!', 'success');
                setTimeout(() => {
                    window.location.href = `booking-confirmation.html?id=${response.data.id}`;
                }, 2000);
            } else {
                utils.showAlert(response.message || 'Failed to create booking', 'error');
            }
        } catch (error) {
            console.error('Error creating booking:', error);
            utils.showAlert('Error creating booking. Please try again.', 'error');
        }
    }

    openImageModal(imageSrc) {
        const modal = document.getElementById('image-modal');
        const modalImage = document.getElementById('modal-image');
        
        modalImage.src = imageSrc;
        modal.classList.remove('hidden');
    }

    showAllImages() {
        // This would open a full gallery view
        console.log('Show all images functionality would be implemented here');
    }

    showReviewForm() {
        if (!utils.isLoggedIn()) {
            utils.showAlert('Please log in to write a review', 'warning');
            setTimeout(() => {
                window.location.href = 'login.html';
            }, 2000);
            return;
        }

        // This would show a review form modal
        console.log('Review form functionality would be implemented here');
    }

    hideLoading() {
        document.getElementById('loading-state').classList.add('hidden');
        document.getElementById('hotel-content').classList.remove('hidden');
    }

    showError(message) {
        document.getElementById('loading-state').innerHTML = `
            <div class="text-center py-12">
                <i class="fas fa-exclamation-triangle text-4xl text-red-400 mb-4"></i>
                <h3 class="text-xl font-medium text-gray-900 mb-2">Error</h3>
                <p class="text-gray-600">${message}</p>
                <a href="hotels.html" class="btn btn-primary mt-4">Back to Hotels</a>
            </div>
        `;
    }
}

// Initialize hotel details manager when DOM is loaded
let hotelDetailsManager;
document.addEventListener('DOMContentLoaded', () => {
    hotelDetailsManager = new HotelDetailsManager();
    
    // Update navigation
    if (window.utils) {
        utils.updateNavigation();
    }
});
