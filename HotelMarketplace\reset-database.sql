-- Reset HotelMarketplace Database
USE master;
GO

-- Drop database if it exists
IF EXISTS (SELECT name FROM sys.databases WHERE name = 'HotelMarketplace')
BEGIN
    ALTER DATABASE HotelMarketplace SET SINGLE_USER WITH ROLLBACK IMMEDIATE;
    DROP DATABASE HotelMarketplace;
END
GO

-- Create new database
CREATE DATABASE HotelMarketplace;
GO

-- Use the new database
USE HotelMarketplace;
GO

PRINT 'Database HotelMarketplace has been reset successfully!';
