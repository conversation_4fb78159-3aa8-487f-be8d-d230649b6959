using HotelMarketplace.Core.DTOs;
using HotelMarketplace.Core.Interfaces;
using HotelMarketplace.Core.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace HotelMarketplace.Application.Services
{
    public class SearchService : ISearchService
    {
        private readonly IRepository<Hotel> _hotelRepository;
        private readonly IRepository<Room> _roomRepository;

        public SearchService(
            IRepository<Hotel> hotelRepository,
            IRepository<Room> roomRepository)
        {
            _hotelRepository = hotelRepository;
            _roomRepository = roomRepository;
        }

        public async Task<ApiResponse<PagedResult<HotelDto>>> SearchHotelsAsync(HotelSearchDto searchDto)
        {
            try
            {
                var hotels = await _hotelRepository.GetAllAsync();
                var query = hotels.Where(h => h.Status == HotelStatus.Active);

                // Apply filters
                if (!string.IsNullOrEmpty(searchDto.Location))
                {
                    query = query.Where(h => h.Location.Contains(searchDto.Location, StringComparison.OrdinalIgnoreCase));
                }

                if (searchDto.CheckIn.HasValue && searchDto.CheckOut.HasValue)
                {
                    // Check room availability (simplified logic)
                    query = query.Where(h => h.Rooms.Any(r => r.IsAvailable));
                }

                var totalCount = query.Count();
                var pagedHotels = query
                    .Skip((searchDto.Page - 1) * searchDto.PageSize)
                    .Take(searchDto.PageSize);

                var hotelDtos = pagedHotels.Select(h => new HotelDto
                {
                    Id = h.Id,
                    Name = h.Name,
                    Location = h.Location,
                    Description = h.Description,
                    Rating = h.Rating,
                    ImageUrl = h.Images?.FirstOrDefault()?.FilePath,
                    LowestPrice = h.Rooms?.Min(r => r.PricePerNight) ?? 0,
                    Status = h.Status.ToString()
                });

                var result = new PagedResult<HotelDto>
                {
                    Items = hotelDtos,
                    TotalCount = totalCount,
                    Page = searchDto.Page,
                    PageSize = searchDto.PageSize
                };

                return ApiResponse<PagedResult<HotelDto>>.Success(result);
            }
            catch (Exception ex)
            {
                return ApiResponse<PagedResult<HotelDto>>.Error($"Error searching hotels: {ex.Message}");
            }
        }

        public async Task<ApiResponse<PagedResult<HotelDto>>> AdvancedHotelSearchAsync(AdvancedHotelSearchDto searchDto)
        {
            try
            {
                var hotels = await _hotelRepository.GetAllAsync();
                var query = hotels.Where(h => h.Status == HotelStatus.Active);

                // Apply basic filters
                if (!string.IsNullOrEmpty(searchDto.Location))
                {
                    query = query.Where(h => h.Location.Contains(searchDto.Location, StringComparison.OrdinalIgnoreCase));
                }

                // Apply advanced filters
                if (searchDto.MinRating.HasValue)
                {
                    query = query.Where(h => h.Rating >= searchDto.MinRating.Value);
                }

                if (searchDto.MaxRating.HasValue)
                {
                    query = query.Where(h => h.Rating <= searchDto.MaxRating.Value);
                }

                if (searchDto.MinPrice.HasValue)
                {
                    query = query.Where(h => h.Rooms.Any(r => r.PricePerNight >= searchDto.MinPrice.Value));
                }

                if (searchDto.MaxPrice.HasValue)
                {
                    query = query.Where(h => h.Rooms.Any(r => r.PricePerNight <= searchDto.MaxPrice.Value));
                }

                // Apply sorting
                switch (searchDto.SortBy?.ToLower())
                {
                    case "price":
                        query = searchDto.SortOrder?.ToLower() == "desc" 
                            ? query.OrderByDescending(h => h.Rooms.Min(r => r.PricePerNight))
                            : query.OrderBy(h => h.Rooms.Min(r => r.PricePerNight));
                        break;
                    case "rating":
                        query = searchDto.SortOrder?.ToLower() == "desc"
                            ? query.OrderByDescending(h => h.Rating)
                            : query.OrderBy(h => h.Rating);
                        break;
                    case "name":
                        query = searchDto.SortOrder?.ToLower() == "desc"
                            ? query.OrderByDescending(h => h.Name)
                            : query.OrderBy(h => h.Name);
                        break;
                    default:
                        query = query.OrderByDescending(h => h.Rating);
                        break;
                }

                var totalCount = query.Count();
                var pagedHotels = query
                    .Skip((searchDto.Page - 1) * searchDto.PageSize)
                    .Take(searchDto.PageSize);

                var hotelDtos = pagedHotels.Select(h => new HotelDto
                {
                    Id = h.Id,
                    Name = h.Name,
                    Location = h.Location,
                    Description = h.Description,
                    Rating = h.Rating,
                    ImageUrl = h.Images?.FirstOrDefault()?.FilePath,
                    LowestPrice = h.Rooms?.Min(r => r.PricePerNight) ?? 0,
                    Status = h.Status.ToString()
                });

                var result = new PagedResult<HotelDto>
                {
                    Items = hotelDtos,
                    TotalCount = totalCount,
                    Page = searchDto.Page,
                    PageSize = searchDto.PageSize
                };

                return ApiResponse<PagedResult<HotelDto>>.Success(result);
            }
            catch (Exception ex)
            {
                return ApiResponse<PagedResult<HotelDto>>.Error($"Error in advanced hotel search: {ex.Message}");
            }
        }

        public async Task<ApiResponse<PagedResult<RoomDto>>> SearchRoomsAsync(RoomSearchDto searchDto)
        {
            try
            {
                var rooms = await _roomRepository.GetAllAsync();
                var query = rooms.Where(r => r.IsAvailable);

                // Apply filters
                if (!string.IsNullOrEmpty(searchDto.Location))
                {
                    query = query.Where(r => r.Hotel.Location.Contains(searchDto.Location, StringComparison.OrdinalIgnoreCase));
                }

                if (searchDto.MinPrice.HasValue)
                {
                    query = query.Where(r => r.PricePerNight >= searchDto.MinPrice.Value);
                }

                if (searchDto.MaxPrice.HasValue)
                {
                    query = query.Where(r => r.PricePerNight <= searchDto.MaxPrice.Value);
                }

                if (searchDto.RoomTypes != null && searchDto.RoomTypes.Any())
                {
                    query = query.Where(r => searchDto.RoomTypes.Contains(r.Type));
                }

                var totalCount = query.Count();
                var pagedRooms = query
                    .Skip((searchDto.Page - 1) * searchDto.PageSize)
                    .Take(searchDto.PageSize);

                var roomDtos = pagedRooms.Select(r => new RoomDto
                {
                    Id = r.Id,
                    HotelId = r.HotelId,
                    HotelName = r.Hotel.Name,
                    RoomType = r.Type.ToString(),
                    PricePerNight = r.PricePerNight,
                    Capacity = r.Capacity,
                    IsAvailable = r.IsAvailable,
                    Amenities = r.Amenities?.Split(',').ToList() ?? new List<string>()
                });

                var result = new PagedResult<RoomDto>
                {
                    Items = roomDtos,
                    TotalCount = totalCount,
                    Page = searchDto.Page,
                    PageSize = searchDto.PageSize
                };

                return ApiResponse<PagedResult<RoomDto>>.Success(result);
            }
            catch (Exception ex)
            {
                return ApiResponse<PagedResult<RoomDto>>.Error($"Error searching rooms: {ex.Message}");
            }
        }

        // Placeholder implementations for other methods
        public Task<ApiResponse<IEnumerable<SearchSuggestionDto>>> GetSearchSuggestionsAsync(string query, int limit)
        {
            var suggestions = new List<SearchSuggestionDto>
            {
                new SearchSuggestionDto { Text = "New York", Type = "location", Count = 150 },
                new SearchSuggestionDto { Text = "Los Angeles", Type = "location", Count = 120 },
                new SearchSuggestionDto { Text = "Chicago", Type = "location", Count = 90 }
            };
            return Task.FromResult(ApiResponse<IEnumerable<SearchSuggestionDto>>.Success(suggestions));
        }

        public Task<ApiResponse<IEnumerable<AutocompleteDto>>> GetAutocompleteAsync(string query, string type)
        {
            var autocomplete = new List<AutocompleteDto>
            {
                new AutocompleteDto { Value = "new-york", Label = "New York", Type = "location" },
                new AutocompleteDto { Value = "los-angeles", Label = "Los Angeles", Type = "location" }
            };
            return Task.FromResult(ApiResponse<IEnumerable<AutocompleteDto>>.Success(autocomplete));
        }

        public Task<ApiResponse<IEnumerable<DestinationDto>>> GetPopularDestinationsAsync(int limit)
        {
            var destinations = new List<DestinationDto>
            {
                new DestinationDto { Name = "New York", Country = "USA", HotelCount = 150, AverageRating = 4.2, AveragePrice = 200 },
                new DestinationDto { Name = "Los Angeles", Country = "USA", HotelCount = 120, AverageRating = 4.1, AveragePrice = 180 }
            };
            return Task.FromResult(ApiResponse<IEnumerable<DestinationDto>>.Success(destinations));
        }

        public Task<ApiResponse<IEnumerable<TrendingSearchDto>>> GetTrendingSearchesAsync(int limit)
        {
            var trending = new List<TrendingSearchDto>
            {
                new TrendingSearchDto { Query = "Beach Resort", SearchCount = 1500, GrowthRate = 25.5, Category = "Resort" },
                new TrendingSearchDto { Query = "City Hotel", SearchCount = 1200, GrowthRate = 15.2, Category = "Hotel" }
            };
            return Task.FromResult(ApiResponse<IEnumerable<TrendingSearchDto>>.Success(trending));
        }

        public Task<ApiResponse<IEnumerable<HotelDto>>> GetNearbyHotelsAsync(double latitude, double longitude, double radiusKm)
        {
            var hotels = new List<HotelDto>();
            return Task.FromResult(ApiResponse<IEnumerable<HotelDto>>.Success(hotels));
        }

        public Task<ApiResponse<SearchFiltersDto>> GetAvailableFiltersAsync(string? location)
        {
            var filters = new SearchFiltersDto();
            return Task.FromResult(ApiResponse<SearchFiltersDto>.Success(filters));
        }

        public Task<ApiResponse<bool>> SaveSearchAsync(SaveSearchDto saveSearchDto)
        {
            return Task.FromResult(ApiResponse<bool>.Success(true));
        }

        public Task<ApiResponse<IEnumerable<SavedSearchDto>>> GetSavedSearchesAsync(string userId)
        {
            var searches = new List<SavedSearchDto>();
            return Task.FromResult(ApiResponse<IEnumerable<SavedSearchDto>>.Success(searches));
        }

        public Task<ApiResponse<bool>> DeleteSavedSearchAsync(int searchId)
        {
            return Task.FromResult(ApiResponse<bool>.Success(true));
        }

        public Task<ApiResponse<bool>> TrackSearchAsync(SearchTrackingDto trackingDto)
        {
            return Task.FromResult(ApiResponse<bool>.Success(true));
        }

        public Task<ApiResponse<SearchAnalyticsDto>> GetSearchAnalyticsAsync(int days)
        {
            var analytics = new SearchAnalyticsDto();
            return Task.FromResult(ApiResponse<SearchAnalyticsDto>.Success(analytics));
        }
    }
}
