using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace HotelMarketplace.Core.Models
{
    public class Promotion
    {
        public int Id { get; set; }

        [Required]
        [MaxLength(200)]
        public string Title { get; set; } = string.Empty;

        [Required]
        [MaxLength(1000)]
        public string Description { get; set; } = string.Empty;

        [Range(0, 100)]
        public decimal DiscountPercentage { get; set; }

        [Range(0, double.MaxValue)]
        public decimal? DiscountAmount { get; set; }

        public DateTime StartDate { get; set; }

        public DateTime EndDate { get; set; }

        public bool IsActive { get; set; }

        public DateTime CreatedAt { get; set; }

        public DateTime? UpdatedAt { get; set; }

        // Foreign Keys
        public string CreatedByUserId { get; set; } = string.Empty;

        // Navigation Properties
        public User CreatedByUser { get; set; } = null!;
        public ICollection<PromotionHotel> PromotionHotels { get; set; } = new List<PromotionHotel>();

        // Additional Properties
        public RoomType? ApplicableRoomType { get; set; }
        public decimal? MinimumBookingAmount { get; set; }
        public int? MaxUsageCount { get; set; }
        public int CurrentUsageCount { get; set; }
        public bool IsGlobal { get; set; } // Applies to all hotels
        public string? PromoCode { get; set; }
        public bool RequiresPromoCode { get; set; }
    }

    public class PromotionHotel
    {
        public int PromotionId { get; set; }
        public int HotelId { get; set; }

        // Navigation Properties
        public Promotion Promotion { get; set; } = null!;
        public Hotel Hotel { get; set; } = null!;
    }
}
