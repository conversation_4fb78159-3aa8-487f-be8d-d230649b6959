using AutoMapper;
using HotelMarketplace.Core.DTOs;
using HotelMarketplace.Core.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;

namespace HotelMarketplace.Application.Services
{
    public class AuthService : IAuthService
    {
        private readonly UserManager<User> _userManager;
        private readonly IMapper _mapper;
        private readonly IConfiguration _configuration;
        private readonly ILogger<AuthService> _logger;

        public AuthService(UserManager<User> userManager, IMapper mapper, IConfiguration configuration, ILogger<AuthService> logger)
        {
            _userManager = userManager;
            _mapper = mapper;
            _configuration = configuration;
            _logger = logger;
        }

        public async Task<ApiResponse<string>> RegisterAsync(UserRegistrationDto registrationDto)
        {
            _logger.LogInformation("Starting user registration for email: {Email}", registrationDto.Email);

            var userExists = await _userManager.FindByEmailAsync(registrationDto.Email);
            if (userExists != null)
            {
                _logger.LogWarning("User registration failed - user already exists: {Email}", registrationDto.Email);
                return ApiResponse<string>.FailureResponse("User already exists!");
            }

            var user = new User
            {
                Email = registrationDto.Email,
                UserName = registrationDto.Email,
                FirstName = registrationDto.FirstName,
                LastName = registrationDto.LastName,
                PhoneNumber = registrationDto.PhoneNumber,
                Role = registrationDto.Role,
                CreatedAt = DateTime.UtcNow,
                IsActive = true
            };

            _logger.LogInformation("Creating user with email: {Email}, Role: {Role}", registrationDto.Email, registrationDto.Role);

            var result = await _userManager.CreateAsync(user, registrationDto.Password);
            if (!result.Succeeded)
            {
                var errors = new List<string>();
                foreach (var error in result.Errors)
                {
                    errors.Add(error.Description);
                    _logger.LogError("User creation error: {Error}", error.Description);
                }
                return ApiResponse<string>.FailureResponse("User creation failed!", errors);
            }

            _logger.LogInformation("User created successfully, adding to role: {Role}", user.Role.ToString());

            // Add user to role
            var roleResult = await _userManager.AddToRoleAsync(user, user.Role.ToString());
            if (!roleResult.Succeeded)
            {
                _logger.LogWarning("Failed to add user to role {Role}: {Errors}", user.Role.ToString(), string.Join(", ", roleResult.Errors.Select(e => e.Description)));
            }

            _logger.LogInformation("User registration completed successfully for: {Email}", registrationDto.Email);
            return ApiResponse<string>.SuccessResponse(GenerateJwtToken(user), "User created successfully!");
        }

        public async Task<ApiResponse<string>> LoginAsync(UserLoginDto loginDto)
        {
            _logger.LogInformation("Login attempt for email: {Email}", loginDto.Email);

            var user = await _userManager.FindByEmailAsync(loginDto.Email);
            if (user == null)
            {
                _logger.LogWarning("Login failed - user not found: {Email}", loginDto.Email);
                return ApiResponse<string>.FailureResponse("Invalid email or password!");
            }

            _logger.LogInformation("User found, checking password for: {Email}", loginDto.Email);

            var result = await _userManager.CheckPasswordAsync(user, loginDto.Password);
            if (!result)
            {
                _logger.LogWarning("Login failed - invalid password for: {Email}", loginDto.Email);
                return ApiResponse<string>.FailureResponse("Invalid email or password!");
            }

            _logger.LogInformation("Login successful for: {Email}", loginDto.Email);
            return ApiResponse<string>.SuccessResponse(GenerateJwtToken(user), "Login successful!");
        }

        public async Task<ApiResponse<UserDto>> GetCurrentUserAsync(string userId)
        {
            var user = await _userManager.FindByIdAsync(userId);
            if (user == null)
                return ApiResponse<UserDto>.FailureResponse("User not found!");

            var userDto = _mapper.Map<UserDto>(user);
            return ApiResponse<UserDto>.SuccessResponse(userDto);
        }

        public async Task<ApiResponse<UserDto>> UpdateUserAsync(string userId, UserUpdateDto updateDto)
        {
            var user = await _userManager.FindByIdAsync(userId);
            if (user == null)
                return ApiResponse<UserDto>.FailureResponse("User not found!");

            user.FirstName = updateDto.FirstName;
            user.LastName = updateDto.LastName;
            user.PhoneNumber = updateDto.PhoneNumber;
            user.UpdatedAt = DateTime.UtcNow;

            var result = await _userManager.UpdateAsync(user);
            if (!result.Succeeded)
            {
                var errors = new List<string>();
                foreach (var error in result.Errors)
                {
                    errors.Add(error.Description);
                }
                return ApiResponse<UserDto>.FailureResponse("User update failed!", errors);
            }

            var userDto = _mapper.Map<UserDto>(user);
            return ApiResponse<UserDto>.SuccessResponse(userDto, "User updated successfully!");
        }

        private string GenerateJwtToken(User user)
        {
            var securityKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_configuration["Jwt:Key"]));
            var credentials = new SigningCredentials(securityKey, SecurityAlgorithms.HmacSha256);

            var claims = new List<Claim>
            {
                new Claim(JwtRegisteredClaimNames.Sub, user.Id),
                new Claim(JwtRegisteredClaimNames.Email, user.Email),
                new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
                new Claim(ClaimTypes.Role, user.Role.ToString()),
                new Claim(JwtRegisteredClaimNames.GivenName, user.FirstName ?? ""),
                new Claim(JwtRegisteredClaimNames.FamilyName, user.LastName ?? ""),
                new Claim("role", user.Role.ToString()) // Additional role claim for easier access
            };

            var token = new JwtSecurityToken(
                issuer: _configuration["Jwt:Issuer"],
                audience: _configuration["Jwt:Audience"],
                claims: claims,
                expires: DateTime.Now.AddHours(3),
                signingCredentials: credentials
            );

            return new JwtSecurityTokenHandler().WriteToken(token);
        }
    }
}
