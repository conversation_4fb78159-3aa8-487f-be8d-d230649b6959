<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Hotel Marketplace API Test</h1>
    
    <div class="test-section">
        <h3>1. Test API Connection</h3>
        <button onclick="testConnection()">Test Connection</button>
        <div id="connection-result"></div>
    </div>
    
    <div class="test-section">
        <h3>2. Test User Registration</h3>
        <button onclick="testRegistration()">Register Test User</button>
        <div id="registration-result"></div>
    </div>
    
    <div class="test-section">
        <h3>3. Test User Login</h3>
        <button onclick="testLogin()">Login Test User</button>
        <div id="login-result"></div>
    </div>
    
    <div class="test-section">
        <h3>4. Test Seeded Users Login</h3>
        <button onclick="testSeededLogin()">Login Admin User</button>
        <div id="seeded-login-result"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:5093/api';
        
        async function testConnection() {
            const resultDiv = document.getElementById('connection-result');
            try {
                const response = await fetch(`${API_BASE_URL}/hotels`);
                if (response.ok) {
                    resultDiv.innerHTML = '<div class="success">✅ API Connection Successful!</div>';
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ API Error: ${response.status} ${response.statusText}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Connection Failed: ${error.message}</div>`;
            }
        }
        
        async function testRegistration() {
            const resultDiv = document.getElementById('registration-result');
            const testUser = {
                firstName: 'Test',
                lastName: 'User',
                email: '<EMAIL>',
                password: 'test123',
                confirmPassword: 'test123'
            };
            
            try {
                const response = await fetch(`${API_BASE_URL}/auth/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testUser)
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `<div class="success">✅ Registration Successful!<pre>${JSON.stringify(data, null, 2)}</pre></div>`;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ Registration Failed:<pre>${JSON.stringify(data, null, 2)}</pre></div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Registration Error: ${error.message}</div>`;
            }
        }
        
        async function testLogin() {
            const resultDiv = document.getElementById('login-result');
            const loginData = {
                email: '<EMAIL>',
                password: 'test123'
            };
            
            try {
                const response = await fetch(`${API_BASE_URL}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(loginData)
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `<div class="success">✅ Login Successful!<pre>${JSON.stringify(data, null, 2)}</pre></div>`;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ Login Failed:<pre>${JSON.stringify(data, null, 2)}</pre></div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Login Error: ${error.message}</div>`;
            }
        }
        
        async function testSeededLogin() {
            const resultDiv = document.getElementById('seeded-login-result');
            const loginData = {
                email: '<EMAIL>',
                password: 'admin123'
            };
            
            try {
                const response = await fetch(`${API_BASE_URL}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(loginData)
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `<div class="success">✅ Seeded Admin Login Successful!<pre>${JSON.stringify(data, null, 2)}</pre></div>`;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ Seeded Admin Login Failed:<pre>${JSON.stringify(data, null, 2)}</pre></div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Seeded Login Error: ${error.message}</div>`;
            }
        }
        
        // Auto-test connection on page load
        window.onload = function() {
            testConnection();
        };
    </script>
</body>
</html>
