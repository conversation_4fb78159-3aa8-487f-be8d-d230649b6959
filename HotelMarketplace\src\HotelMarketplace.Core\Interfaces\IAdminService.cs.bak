using HotelMarketplace.Core.DTOs;
using HotelMarketplace.Core.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace HotelMarketplace.Core.Interfaces
{
    public interface IAdminService
    {
        // Dashboard and Statistics
        Task<ApiResponse<AdminDashboardDto>> GetDashboardStatsAsync();
        Task<ApiResponse<AnalyticsOverviewDto>> GetAnalyticsOverviewAsync(int days);
        Task<ApiResponse<SystemHealthDto>> GetSystemHealthAsync();

        // User Management
        Task<ApiResponse<PagedResult<UserDto>>> GetAllUsersAsync(int page, int pageSize);
        Task<ApiResponse<UserDto>> GetUserByIdAsync(string userId);
        Task<ApiResponse<bool>> UpdateUserStatusAsync(string userId, UserStatusUpdateDto statusDto);
        Task<ApiResponse<bool>> DeleteUserAsync(string userId, string adminId);

        // Hotel Management
        Task<ApiResponse<IEnumerable<HotelDto>>> GetPendingHotelsAsync();
        Task<ApiResponse<bool>> ApproveHotelAsync(int hotelId, string adminId);
        Task<ApiResponse<bool>> RejectHotelAsync(int hotelId, string adminId, string reason);
        Task<ApiResponse<bool>> SuspendHotelAsync(int hotelId, string adminId, string reason);

        // Review Management
        Task<ApiResponse<IEnumerable<ReviewDto>>> GetPendingReviewsAsync();
        Task<ApiResponse<bool>> ApproveReviewAsync(int reviewId, string adminId);
        Task<ApiResponse<bool>> RejectReviewAsync(int reviewId, string adminId, string reason);

        // Booking Management
        Task<ApiResponse<PagedResult<BookingDto>>> GetAllBookingsAsync(int page, int pageSize);
        Task<ApiResponse<bool>> CancelBookingAsync(int bookingId, string adminId, string reason);

        // Reports
        Task<ApiResponse<RevenueReportDto>> GetRevenueReportAsync(DateTime? startDate, DateTime? endDate);
        Task<ApiResponse<BookingReportDto>> GetBookingReportAsync(DateTime? startDate, DateTime? endDate);
        Task<ApiResponse<HotelReportDto>> GetHotelReportAsync();
        Task<ApiResponse<UserReportDto>> GetUserReportAsync();

        // Notifications
        Task<ApiResponse<bool>> BroadcastNotificationAsync(string adminId, BroadcastNotificationDto notificationDto);
        Task<ApiResponse<IEnumerable<NotificationDto>>> GetSystemNotificationsAsync();

        // System Management
        Task<ApiResponse<bool>> CreateSystemBackupAsync(string adminId);
        Task<ApiResponse<IEnumerable<SystemLogDto>>> GetSystemLogsAsync(int page, int pageSize);
        Task<ApiResponse<bool>> ClearSystemLogsAsync(string adminId);

        // Content Management
        Task<ApiResponse<bool>> UpdateSystemSettingsAsync(string adminId, SystemSettingsDto settings);
        Task<ApiResponse<SystemSettingsDto>> GetSystemSettingsAsync();
        Task<ApiResponse<bool>> ManagePromotionsAsync(string adminId, PromotionDto promotion);
    }
}
