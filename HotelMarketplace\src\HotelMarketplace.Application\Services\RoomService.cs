using AutoMapper;
using HotelMarketplace.Core.DTOs;
using HotelMarketplace.Core.Interfaces;
using HotelMarketplace.Core.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace HotelMarketplace.Application.Services
{
    public class RoomService : IRoomService
    {
        private readonly IRoomRepository _roomRepository;
        private readonly IHotelRepository _hotelRepository;
        private readonly IMapper _mapper;

        public RoomService(
            IRoomRepository roomRepository,
            IHotelRepository hotelRepository,
            IMapper mapper)
        {
            _roomRepository = roomRepository;
            _hotelRepository = hotelRepository;
            _mapper = mapper;
        }

        public async Task<ApiResponse<IEnumerable<RoomDto>>> GetRoomsByHotelIdAsync(int hotelId)
        {
            var rooms = await _roomRepository.GetRoomsByHotelIdAsync(hotelId);
            var roomDtos = _mapper.Map<IEnumerable<RoomDto>>(rooms);
            return ApiResponse<IEnumerable<RoomDto>>.SuccessResponse(roomDtos);
        }

        public async Task<ApiResponse<RoomDto>> GetRoomByIdAsync(int id)
        {
            var room = await _roomRepository.GetRoomWithDetailsAsync(id);
            if (room == null)
                return ApiResponse<RoomDto>.FailureResponse("Room not found!");

            var roomDto = _mapper.Map<RoomDto>(room);
            return ApiResponse<RoomDto>.SuccessResponse(roomDto);
        }

        public async Task<ApiResponse<RoomDto>> CreateRoomAsync(string ownerId, RoomCreateDto createDto)
        {
            var hotel = await _hotelRepository.GetByIdAsync(createDto.HotelId);
            if (hotel == null)
                return ApiResponse<RoomDto>.FailureResponse("Hotel not found!");

            if (hotel.OwnerId != ownerId)
                return ApiResponse<RoomDto>.FailureResponse("You are not authorized to add rooms to this hotel!");

            var room = _mapper.Map<Room>(createDto);
            room.CreatedAt = DateTime.UtcNow;

            await _roomRepository.AddAsync(room);

            var roomDto = _mapper.Map<RoomDto>(room);
            return ApiResponse<RoomDto>.SuccessResponse(roomDto, "Room created successfully!");
        }

        public async Task<ApiResponse<RoomDto>> UpdateRoomAsync(int id, string ownerId, RoomUpdateDto updateDto)
        {
            var room = await _roomRepository.GetRoomWithDetailsAsync(id);
            if (room == null)
                return ApiResponse<RoomDto>.FailureResponse("Room not found!");

            if (room.Hotel.OwnerId != ownerId)
                return ApiResponse<RoomDto>.FailureResponse("You are not authorized to update this room!");

            room.RoomType = updateDto.RoomType;
            room.PricePerNight = updateDto.PricePerNight;
            room.TotalRooms = updateDto.TotalRooms;
            room.Amenities = updateDto.Amenities != null ? string.Join(",", updateDto.Amenities) : string.Empty;
            room.UpdatedAt = DateTime.UtcNow;

            await _roomRepository.UpdateAsync(room);

            var roomDto = _mapper.Map<RoomDto>(room);
            return ApiResponse<RoomDto>.SuccessResponse(roomDto, "Room updated successfully!");
        }

        public async Task<ApiResponse<bool>> DeleteRoomAsync(int id, string ownerId)
        {
            var room = await _roomRepository.GetRoomWithDetailsAsync(id);
            if (room == null)
                return ApiResponse<bool>.FailureResponse("Room not found!");

            if (room.Hotel.OwnerId != ownerId)
                return ApiResponse<bool>.FailureResponse("You are not authorized to delete this room!");

            await _roomRepository.RemoveAsync(room);
            return ApiResponse<bool>.SuccessResponse(true, "Room deleted successfully!");
        }

        public async Task<ApiResponse<RoomAvailabilityDto>> CheckRoomAvailabilityAsync(int roomId, DateTime checkIn, DateTime checkOut)
        {
            if (checkIn >= checkOut)
                return ApiResponse<RoomAvailabilityDto>.FailureResponse("Check-in date must be before check-out date!");

            if (checkIn < DateTime.Today)
                return ApiResponse<RoomAvailabilityDto>.FailureResponse("Check-in date cannot be in the past!");

            var availableCount = await _roomRepository.GetAvailableRoomCountAsync(roomId, checkIn, checkOut);

            var availabilityDto = new RoomAvailabilityDto
            {
                RoomId = roomId,
                CheckIn = checkIn,
                CheckOut = checkOut,
                AvailableCount = availableCount
            };

            return ApiResponse<RoomAvailabilityDto>.SuccessResponse(availabilityDto);
        }
    }
}
