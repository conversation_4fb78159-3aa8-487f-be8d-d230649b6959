// API configuration
const API_BASE_URL = 'http://localhost:5093/api';
const USE_MOCK_DATA = false; // Set to false when backend is ready
const TOKEN_KEY = 'hotel_marketplace_token';

// Mock data for development
const mockData = {
    hotels: [
        {
            id: 1,
            name: "Grand Plaza Hotel",
            location: "New York, NY",
            description: "Luxury hotel in the heart of Manhattan with stunning city views and world-class amenities.",
            rating: 4.5,
            imageUrl: "https://images.unsplash.com/photo-1566073771259-6a8506099945?w=500",
            lowestPrice: 299,
            status: "Active",
            amenities: ["WiFi", "Pool", "Gym", "Spa", "Restaurant", "Room Service"],
            images: [
                "https://images.unsplash.com/photo-1566073771259-6a8506099945?w=500",
                "https://images.unsplash.com/photo-1571003123894-1f0594d2b5d9?w=500",
                "https://images.unsplash.com/photo-1520250497591-112f2f40a3f4?w=500"
            ]
        },
        {
            id: 2,
            name: "Ocean View Resort",
            location: "Miami, FL",
            description: "Beautiful beachfront resort with pristine ocean views and tropical paradise atmosphere.",
            rating: 4.3,
            imageUrl: "https://images.unsplash.com/photo-1571003123894-1f0594d2b5d9?w=500",
            lowestPrice: 199,
            status: "Active",
            amenities: ["WiFi", "Beach Access", "Pool", "Restaurant", "Bar", "Water Sports"],
            images: [
                "https://images.unsplash.com/photo-1571003123894-1f0594d2b5d9?w=500",
                "https://images.unsplash.com/photo-1566073771259-6a8506099945?w=500"
            ]
        },
        {
            id: 3,
            name: "Mountain Lodge",
            location: "Denver, CO",
            description: "Cozy mountain retreat perfect for outdoor enthusiasts and nature lovers.",
            rating: 4.7,
            imageUrl: "https://images.unsplash.com/photo-1520250497591-112f2f40a3f4?w=500",
            lowestPrice: 149,
            status: "Active",
            amenities: ["WiFi", "Fireplace", "Hiking Trails", "Restaurant", "Ski Access"],
            images: [
                "https://images.unsplash.com/photo-1520250497591-112f2f40a3f4?w=500"
            ]
        }
    ],
    rooms: [
        {
            id: 1,
            hotelId: 1,
            type: "Standard Room",
            pricePerNight: 299,
            capacity: 2,
            isAvailable: true,
            amenities: ["WiFi", "TV", "Air Conditioning", "Mini Bar"],
            description: "Comfortable standard room with city views",
            bedType: "Queen Bed",
            size: "350 sq ft",
            view: "City View"
        },
        {
            id: 2,
            hotelId: 1,
            type: "Deluxe Suite",
            pricePerNight: 499,
            capacity: 4,
            isAvailable: true,
            amenities: ["WiFi", "TV", "Air Conditioning", "Balcony", "Mini Bar", "Living Area"],
            description: "Spacious suite with separate living area and balcony",
            bedType: "King Bed + Sofa Bed",
            size: "650 sq ft",
            view: "City View"
        },
        {
            id: 3,
            hotelId: 2,
            type: "Ocean View Room",
            pricePerNight: 199,
            capacity: 2,
            isAvailable: true,
            amenities: ["WiFi", "TV", "Ocean View", "Balcony"],
            description: "Beautiful room with direct ocean views",
            bedType: "Queen Bed",
            size: "400 sq ft",
            view: "Ocean View"
        },
        {
            id: 4,
            hotelId: 3,
            type: "Mountain View Room",
            pricePerNight: 149,
            capacity: 2,
            isAvailable: true,
            amenities: ["WiFi", "TV", "Mountain View", "Fireplace"],
            description: "Cozy room with stunning mountain views",
            bedType: "Queen Bed",
            size: "300 sq ft",
            view: "Mountain View"
        }
    ],
    reviews: [
        {
            id: 1,
            hotelId: 1,
            userId: "1",
            userName: "John Doe",
            rating: 5,
            comment: "Excellent hotel with amazing service and beautiful rooms. Highly recommended!",
            createdAt: new Date(Date.now() - 86400000).toISOString(),
            isApproved: true
        },
        {
            id: 2,
            hotelId: 2,
            userId: "2",
            userName: "Jane Smith",
            rating: 4,
            comment: "Great location right on the beach. The ocean views were spectacular.",
            createdAt: new Date(Date.now() - 172800000).toISOString(),
            isApproved: true
        }
    ]
};

// Utility functions
const getToken = () => localStorage.getItem(TOKEN_KEY);
const setToken = (token) => localStorage.setItem(TOKEN_KEY, token);
const removeToken = () => localStorage.removeItem(TOKEN_KEY);
const isLoggedIn = () => !!getToken();

const getUserRole = () => {
    const token = getToken();
    if (!token) return null;

    // For mock data, check localStorage
    if (USE_MOCK_DATA) {
        return localStorage.getItem('userRole') || 'Customer';
    }

    try {
        const payload = token.split('.')[1];
        const decodedPayload = atob(payload);
        const userData = JSON.parse(decodedPayload);
        return userData.role;
    } catch (error) {
        console.error('Error parsing token:', error);
        return null;
    }
};

// Mock request handler
const mockRequest = async (endpoint, method = 'GET', data = null) => {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 300));

    console.log(`Mock API request: ${method} ${endpoint}`, data);

    // Handle different endpoints
    if (endpoint === 'hotels' && method === 'GET') {
        return {
            success: true,
            data: {
                items: mockData.hotels,
                totalCount: mockData.hotels.length,
                page: 1,
                pageSize: 10
            }
        };
    }

    if (endpoint.startsWith('hotels/') && method === 'GET') {
        const hotelId = parseInt(endpoint.split('/')[1]);
        const hotel = mockData.hotels.find(h => h.id === hotelId);
        if (hotel) {
            return {
                success: true,
                data: hotel
            };
        }
        return {
            success: false,
            message: 'Hotel not found'
        };
    }

    if (endpoint.startsWith('rooms/hotel/') && method === 'GET') {
        const hotelId = parseInt(endpoint.split('/')[2]);
        const rooms = mockData.rooms.filter(r => r.hotelId === hotelId);
        return {
            success: true,
            data: rooms
        };
    }

    if (endpoint.startsWith('reviews/hotel/') && method === 'GET') {
        const hotelId = parseInt(endpoint.split('/')[2]);
        const reviews = mockData.reviews.filter(r => r.hotelId === hotelId);
        return {
            success: true,
            data: reviews
        };
    }

    if (endpoint === 'auth/login' && method === 'POST') {
        const { email, password } = data;
        if (email === '<EMAIL>' && password === 'admin123') {
            const token = 'mock-jwt-token-admin';
            setToken(token);
            localStorage.setItem('userRole', 'Admin');
            localStorage.setItem('userName', 'Admin User');
            return {
                success: true,
                data: { token, user: { id: '1', email, role: 'Admin' } }
            };
        }
        if (email === '<EMAIL>' && password === 'user123') {
            const token = 'mock-jwt-token-user';
            setToken(token);
            localStorage.setItem('userRole', 'Customer');
            localStorage.setItem('userName', 'John Doe');
            return {
                success: true,
                data: { token, user: { id: '2', email, role: 'Customer' } }
            };
        }
        return {
            success: false,
            message: 'Invalid credentials'
        };
    }

    if (endpoint === 'auth/register' && method === 'POST') {
        return {
            success: true,
            data: { message: 'User registered successfully' }
        };
    }

    // Default mock response
    return {
        success: true,
        data: {}
    };
};

// API request helper
const apiRequest = async (endpoint, method = 'GET', data = null) => {
    // Use mock data if enabled
    if (USE_MOCK_DATA) {
        return mockRequest(endpoint, method, data);
    }

    const url = `${API_BASE_URL}/${endpoint}`;
    const headers = {
        'Content-Type': 'application/json',
    };

    const token = getToken();
    if (token) {
        headers['Authorization'] = `Bearer ${token}`;
    }

    const options = {
        method,
        headers,
    };

    if (data) {
        options.body = JSON.stringify(data);
    }

    try {
        console.log(`Making API request to: ${url}`);
        const response = await fetch(url, options);

        if (response.status === 401) {
            removeToken();
            window.location.href = '/login.html';
            return null;
        }

        let result;
        try {
            result = await response.json();
        } catch (error) {
            console.error('Error parsing JSON response:', error);
            throw new Error('Invalid response from server');
        }

        if (!response.ok) {
            throw new Error(result.message || 'Something went wrong');
        }

        return result;
    } catch (error) {
        console.error('API request error:', error);
        throw error;
    }
};

// Authentication API
const authAPI = {
    login: (email, password) => apiRequest('auth/login', 'POST', { email, password }),
    register: (userData) => apiRequest('auth/register', 'POST', userData),
    getCurrentUser: () => apiRequest('auth/me'),
    updateUser: (userData) => apiRequest('auth/me', 'PUT', userData),
};

// Hotels API
const hotelsAPI = {
    getAllHotels: () => apiRequest('hotels'),
    getHotelById: (id) => apiRequest(`hotels/${id}`),
    searchHotels: (searchParams) => {
        const queryString = new URLSearchParams(searchParams).toString();
        return apiRequest(`hotels/search?${queryString}`);
    },
    getMyHotels: () => apiRequest('hotels/my-hotels'),
    createHotel: (hotelData) => apiRequest('hotels', 'POST', hotelData),
    updateHotel: (id, hotelData) => apiRequest(`hotels/${id}`, 'PUT', hotelData),
    deleteHotel: (id) => apiRequest(`hotels/${id}`, 'DELETE'),
};

// Rooms API
const roomsAPI = {
    getRoomsByHotelId: (hotelId) => apiRequest(`rooms/hotel/${hotelId}`),
    getRoomById: (id) => apiRequest(`rooms/${id}`),
    createRoom: (roomData) => apiRequest('rooms', 'POST', roomData),
    updateRoom: (id, roomData) => apiRequest(`rooms/${id}`, 'PUT', roomData),
    deleteRoom: (id) => apiRequest(`rooms/${id}`, 'DELETE'),
    checkRoomAvailability: (id, checkIn, checkOut) => {
        const queryString = new URLSearchParams({ checkIn, checkOut }).toString();
        return apiRequest(`rooms/${id}/availability?${queryString}`);
    },
};

// Bookings API
const bookingsAPI = {
    getMyBookings: () => apiRequest('bookings'),
    getBookingsByHotelId: (hotelId) => apiRequest(`bookings/hotel/${hotelId}`),
    getBookingById: (id) => apiRequest(`bookings/${id}`),
    createBooking: (bookingData) => apiRequest('bookings', 'POST', bookingData),
    updateBookingStatus: (id, status) => apiRequest(`bookings/${id}/status`, 'PUT', { status }),
    cancelBooking: (id) => apiRequest(`bookings/${id}/cancel`, 'POST'),
    emailConfirmation: (id) => apiRequest(`bookings/${id}/email-confirmation`, 'POST'),
};

// Reviews API
const reviewsAPI = {
    getReviewsByHotelId: (hotelId) => apiRequest(`reviews/hotel/${hotelId}`),
    getMyReviews: () => apiRequest('reviews/user'),
    createReview: (reviewData) => apiRequest('reviews', 'POST', reviewData),
    updateReview: (id, reviewData) => apiRequest(`reviews/${id}`, 'PUT', reviewData),
    deleteReview: (id) => apiRequest(`reviews/${id}`, 'DELETE'),
    approveReview: (id) => apiRequest(`reviews/${id}/approve`, 'POST'),
};

// Admin API
const adminAPI = {
    getDashboardStats: () => apiRequest('admin/dashboard'),
    getAllUsers: (params) => {
        const queryString = new URLSearchParams(params).toString();
        return apiRequest(`admin/users?${queryString}`);
    },
    updateUserStatus: (userId, statusData) => apiRequest(`admin/users/${userId}/status`, 'PUT', statusData),
    getPendingHotels: () => apiRequest('admin/hotels/pending'),
    getAllHotels: (params) => {
        const queryString = new URLSearchParams(params).toString();
        return apiRequest(`admin/hotels?${queryString}`);
    },
    approveHotel: (hotelId) => apiRequest(`admin/hotels/${hotelId}/approve`, 'POST'),
    rejectHotel: (hotelId, data) => apiRequest(`admin/hotels/${hotelId}/reject`, 'POST', data),
};

// Export all API functions
window.api = {
    auth: authAPI,
    hotels: hotelsAPI,
    rooms: roomsAPI,
    bookings: bookingsAPI,
    reviews: reviewsAPI,
    admin: adminAPI,
    utils: {
        getToken,
        setToken,
        removeToken,
        isLoggedIn,
        getUserRole,
    },
};
