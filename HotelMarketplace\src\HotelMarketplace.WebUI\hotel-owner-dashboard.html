<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hotel Owner Dashboard - Hotel Marketplace</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="css/output.css" rel="stylesheet">
    <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="bg-gray-50 font-sans">
    <!-- Alert Container -->
    <div id="alert-container" class="fixed top-4 right-4 z-50 w-80"></div>

    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="container mx-auto px-4 py-3">
            <div class="flex justify-between items-center">
                <div class="flex items-center">
                    <a href="index.html" class="text-2xl font-bold text-primary-600 mr-8">HotelMarketplace</a>
                    <span class="text-gray-600">Hotel Owner Dashboard</span>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="relative">
                        <button id="notifications-btn" class="relative p-2 text-gray-600 hover:text-gray-900">
                            <i class="fas fa-bell text-xl"></i>
                            <span id="notification-badge" class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center hidden">0</span>
                        </button>
                    </div>
                    <div class="relative group">
                        <button class="flex items-center space-x-2 text-gray-700 hover:text-gray-900">
                            <i class="fas fa-user-circle text-xl"></i>
                            <span id="user-name">Hotel Owner</span>
                            <i class="fas fa-chevron-down text-sm"></i>
                        </button>
                        <div class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10 hidden group-hover:block">
                            <a href="#profile" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Profile</a>
                            <a href="#settings" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Settings</a>
                            <hr class="my-1">
                            <a href="#" onclick="utils.logout()" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Logout</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <div class="flex">
        <!-- Sidebar -->
        <aside class="w-64 bg-white shadow-sm min-h-screen">
            <nav class="p-4">
                <ul class="space-y-2">
                    <li>
                        <a href="#dashboard" class="nav-link active" data-section="dashboard">
                            <i class="fas fa-tachometer-alt mr-3"></i>
                            Dashboard
                        </a>
                    </li>
                    <li>
                        <a href="#hotels" class="nav-link" data-section="hotels">
                            <i class="fas fa-hotel mr-3"></i>
                            My Hotels
                        </a>
                    </li>
                    <li>
                        <a href="#rooms" class="nav-link" data-section="rooms">
                            <i class="fas fa-bed mr-3"></i>
                            Rooms
                        </a>
                    </li>
                    <li>
                        <a href="#bookings" class="nav-link" data-section="bookings">
                            <i class="fas fa-calendar-check mr-3"></i>
                            Bookings
                        </a>
                    </li>
                    <li>
                        <a href="#reviews" class="nav-link" data-section="reviews">
                            <i class="fas fa-star mr-3"></i>
                            Reviews
                        </a>
                    </li>
                    <li>
                        <a href="#analytics" class="nav-link" data-section="analytics">
                            <i class="fas fa-chart-bar mr-3"></i>
                            Analytics
                        </a>
                    </li>
                    <li>
                        <a href="#profile" class="nav-link" data-section="profile">
                            <i class="fas fa-user mr-3"></i>
                            Profile
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="flex-1 p-6">
            <!-- Dashboard Section -->
            <section id="dashboard-section" class="dashboard-section">
                <div class="mb-6">
                    <h1 class="text-2xl font-bold text-gray-900">Dashboard Overview</h1>
                    <p class="text-gray-600">Welcome back! Here's how your hotels are performing.</p>
                </div>

                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                                <i class="fas fa-hotel text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">My Hotels</p>
                                <p id="total-hotels" class="text-2xl font-bold text-gray-900">0</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-green-100 text-green-600">
                                <i class="fas fa-calendar-check text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">Total Bookings</p>
                                <p id="total-bookings" class="text-2xl font-bold text-gray-900">0</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                                <i class="fas fa-dollar-sign text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">Monthly Revenue</p>
                                <p id="monthly-revenue" class="text-2xl font-bold text-gray-900">$0</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                                <i class="fas fa-star text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">Avg Rating</p>
                                <p id="avg-rating" class="text-2xl font-bold text-gray-900">0.0</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <h3 class="text-lg font-semibold mb-4">Quick Actions</h3>
                        <div class="space-y-3">
                            <button onclick="showAddHotelModal()" class="w-full flex items-center p-3 rounded-lg border hover:bg-gray-50">
                                <i class="fas fa-plus text-primary-600 mr-3"></i>
                                <span>Add New Hotel</span>
                            </button>
                            <a href="#rooms" class="flex items-center p-3 rounded-lg border hover:bg-gray-50">
                                <i class="fas fa-bed text-primary-600 mr-3"></i>
                                <span>Manage Rooms</span>
                            </a>
                            <a href="#bookings" class="flex items-center p-3 rounded-lg border hover:bg-gray-50">
                                <i class="fas fa-calendar text-primary-600 mr-3"></i>
                                <span>View Bookings</span>
                            </a>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <h3 class="text-lg font-semibold mb-4">Recent Activity</h3>
                        <div id="recent-activity" class="space-y-3">
                            <p class="text-gray-500">No recent activity</p>
                        </div>
                    </div>
                </div>

                <!-- Revenue Chart -->
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-semibold mb-4">Revenue Trends</h3>
                    <canvas id="revenue-chart" width="400" height="200"></canvas>
                </div>
            </section>

            <!-- Hotels Section -->
            <section id="hotels-section" class="dashboard-section hidden">
                <div class="mb-6">
                    <div class="flex justify-between items-center">
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900">My Hotels</h1>
                            <p class="text-gray-600">Manage your hotel listings.</p>
                        </div>
                        <button onclick="showAddHotelModal()" class="btn btn-primary">
                            <i class="fas fa-plus mr-2"></i>
                            Add Hotel
                        </button>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                    <div class="p-6 border-b">
                        <h3 class="text-lg font-semibold">Hotel Listings</h3>
                    </div>
                    <div id="hotels-list" class="p-6">
                        <p class="text-gray-500 text-center py-8">No hotels yet. Add your first hotel to get started!</p>
                    </div>
                </div>
            </section>

            <!-- Rooms Section -->
            <section id="rooms-section" class="dashboard-section hidden">
                <div class="mb-6">
                    <div class="flex justify-between items-center">
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900">Room Management</h1>
                            <p class="text-gray-600">Manage rooms across all your hotels.</p>
                        </div>
                        <button onclick="showAddRoomModal()" class="btn btn-primary">
                            <i class="fas fa-plus mr-2"></i>
                            Add Room
                        </button>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                    <div class="p-6 border-b">
                        <div class="flex justify-between items-center">
                            <h3 class="text-lg font-semibold">All Rooms</h3>
                            <select id="hotel-filter" class="input">
                                <option value="">All Hotels</option>
                            </select>
                        </div>
                    </div>
                    <div id="rooms-list" class="p-6">
                        <p class="text-gray-500 text-center py-8">No rooms yet. Add a hotel first, then add rooms to it!</p>
                    </div>
                </div>
            </section>

            <!-- Bookings Section -->
            <section id="bookings-section" class="dashboard-section hidden">
                <div class="mb-6">
                    <h1 class="text-2xl font-bold text-gray-900">Booking Management</h1>
                    <p class="text-gray-600">Manage reservations for your hotels.</p>
                </div>

                <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                    <div class="p-6 border-b">
                        <div class="flex justify-between items-center">
                            <h3 class="text-lg font-semibold">All Bookings</h3>
                            <select id="booking-status-filter" class="input">
                                <option value="">All Status</option>
                                <option value="Pending">Pending</option>
                                <option value="Confirmed">Confirmed</option>
                                <option value="Completed">Completed</option>
                                <option value="Cancelled">Cancelled</option>
                            </select>
                        </div>
                    </div>
                    <div id="bookings-list" class="p-6">
                        <p class="text-gray-500 text-center py-8">No bookings yet. Once customers book your hotels, they'll appear here!</p>
                    </div>
                </div>
            </section>

            <!-- Reviews Section -->
            <section id="reviews-section" class="dashboard-section hidden">
                <div class="mb-6">
                    <h1 class="text-2xl font-bold text-gray-900">Customer Reviews</h1>
                    <p class="text-gray-600">Reviews for your hotels.</p>
                </div>

                <div class="bg-white rounded-lg shadow-sm p-6">
                    <div id="reviews-list">
                        <p class="text-gray-500 text-center py-8">No reviews yet. Great reviews will help attract more customers!</p>
                    </div>
                </div>
            </section>

            <!-- Analytics Section -->
            <section id="analytics-section" class="dashboard-section hidden">
                <div class="mb-6">
                    <h1 class="text-2xl font-bold text-gray-900">Analytics & Reports</h1>
                    <p class="text-gray-600">Detailed insights about your hotel performance.</p>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <h3 class="text-lg font-semibold mb-4">Booking Trends</h3>
                        <canvas id="booking-chart" width="400" height="200"></canvas>
                    </div>

                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <h3 class="text-lg font-semibold mb-4">Revenue by Hotel</h3>
                        <canvas id="hotel-revenue-chart" width="400" height="200"></canvas>
                    </div>
                </div>
            </section>

            <!-- Profile Section -->
            <section id="profile-section" class="dashboard-section hidden">
                <div class="mb-6">
                    <h1 class="text-2xl font-bold text-gray-900">My Profile</h1>
                    <p class="text-gray-600">Manage your account information.</p>
                </div>

                <div class="bg-white rounded-lg shadow-sm p-6">
                    <form id="profile-form">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="firstName" class="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                                <input type="text" id="firstName" name="firstName" class="input" required>
                            </div>
                            <div>
                                <label for="lastName" class="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                                <input type="text" id="lastName" name="lastName" class="input" required>
                            </div>
                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                                <input type="email" id="email" name="email" class="input" required readonly>
                            </div>
                            <div>
                                <label for="phoneNumber" class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                                <input type="tel" id="phoneNumber" name="phoneNumber" class="input">
                            </div>
                        </div>
                        <div class="mt-6">
                            <button type="submit" class="btn btn-primary">Update Profile</button>
                        </div>
                    </form>
                </div>
            </section>
        </main>
    </div>

    <!-- Add Hotel Modal -->
    <div id="add-hotel-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-bold text-gray-900">Add New Hotel</h3>
                    <button onclick="closeModal('add-hotel-modal')" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <form id="add-hotel-form" class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="hotel-name" class="block text-sm font-medium text-gray-700 mb-2">Hotel Name *</label>
                            <input type="text" id="hotel-name" name="name" class="input" required>
                        </div>
                        <div>
                            <label for="hotel-city" class="block text-sm font-medium text-gray-700 mb-2">City *</label>
                            <input type="text" id="hotel-city" name="city" class="input" required>
                        </div>
                    </div>

                    <div>
                        <label for="hotel-address" class="block text-sm font-medium text-gray-700 mb-2">Address *</label>
                        <input type="text" id="hotel-address" name="address" class="input" required>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label for="hotel-state" class="block text-sm font-medium text-gray-700 mb-2">State *</label>
                            <input type="text" id="hotel-state" name="state" class="input" required>
                        </div>
                        <div>
                            <label for="hotel-country" class="block text-sm font-medium text-gray-700 mb-2">Country *</label>
                            <input type="text" id="hotel-country" name="country" class="input" required>
                        </div>
                        <div>
                            <label for="hotel-zipcode" class="block text-sm font-medium text-gray-700 mb-2">Zip Code</label>
                            <input type="text" id="hotel-zipcode" name="zipCode" class="input">
                        </div>
                    </div>

                    <div>
                        <label for="hotel-description" class="block text-sm font-medium text-gray-700 mb-2">Description *</label>
                        <textarea id="hotel-description" name="description" rows="4" class="input" required></textarea>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="hotel-phone" class="block text-sm font-medium text-gray-700 mb-2">Phone</label>
                            <input type="tel" id="hotel-phone" name="phone" class="input">
                        </div>
                        <div>
                            <label for="hotel-email" class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                            <input type="email" id="hotel-email" name="email" class="input">
                        </div>
                    </div>

                    <div>
                        <label for="hotel-website" class="block text-sm font-medium text-gray-700 mb-2">Website</label>
                        <input type="url" id="hotel-website" name="website" class="input">
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="hotel-latitude" class="block text-sm font-medium text-gray-700 mb-2">Latitude</label>
                            <input type="number" step="any" id="hotel-latitude" name="latitude" class="input">
                        </div>
                        <div>
                            <label for="hotel-longitude" class="block text-sm font-medium text-gray-700 mb-2">Longitude</label>
                            <input type="number" step="any" id="hotel-longitude" name="longitude" class="input">
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="hotel-checkin" class="block text-sm font-medium text-gray-700 mb-2">Check-in Time</label>
                            <input type="time" id="hotel-checkin" name="checkInTime" value="15:00" class="input">
                        </div>
                        <div>
                            <label for="hotel-checkout" class="block text-sm font-medium text-gray-700 mb-2">Check-out Time</label>
                            <input type="time" id="hotel-checkout" name="checkOutTime" value="11:00" class="input">
                        </div>
                    </div>

                    <div>
                        <label for="hotel-amenities" class="block text-sm font-medium text-gray-700 mb-2">Amenities (comma-separated)</label>
                        <input type="text" id="hotel-amenities" name="amenities" class="input" placeholder="WiFi, Pool, Gym, Spa, Restaurant">
                    </div>

                    <div>
                        <label for="hotel-cancellation" class="block text-sm font-medium text-gray-700 mb-2">Cancellation Policy</label>
                        <textarea id="hotel-cancellation" name="cancellationPolicy" rows="3" class="input"></textarea>
                    </div>

                    <div class="flex justify-end space-x-4 pt-4">
                        <button type="button" onclick="closeModal('add-hotel-modal')" class="btn btn-secondary">Cancel</button>
                        <button type="submit" class="btn btn-primary">Add Hotel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Add Room Modal -->
    <div id="add-room-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-bold text-gray-900">Add New Room</h3>
                    <button onclick="closeModal('add-room-modal')" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <form id="add-room-form" class="space-y-4">
                    <div>
                        <label for="room-hotel-select" class="block text-sm font-medium text-gray-700 mb-2">Select Hotel *</label>
                        <select id="room-hotel-select" name="hotelId" class="input" required>
                            <option value="">Select a hotel</option>
                        </select>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="room-type" class="block text-sm font-medium text-gray-700 mb-2">Room Type *</label>
                            <select id="room-type" name="roomType" class="input" required>
                                <option value="">Select room type</option>
                                <option value="0">Single</option>
                                <option value="1">Double</option>
                                <option value="2">Twin</option>
                                <option value="3">Suite</option>
                                <option value="4">Deluxe</option>
                                <option value="5">Presidential</option>
                            </select>
                        </div>
                        <div>
                            <label for="room-number" class="block text-sm font-medium text-gray-700 mb-2">Room Number</label>
                            <input type="text" id="room-number" name="roomNumber" class="input">
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="room-price" class="block text-sm font-medium text-gray-700 mb-2">Price per Night *</label>
                            <input type="number" step="0.01" id="room-price" name="pricePerNight" class="input" required>
                        </div>
                        <div>
                            <label for="room-discount-price" class="block text-sm font-medium text-gray-700 mb-2">Discounted Price</label>
                            <input type="number" step="0.01" id="room-discount-price" name="discountedPrice" class="input">
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label for="room-total" class="block text-sm font-medium text-gray-700 mb-2">Total Rooms *</label>
                            <input type="number" id="room-total" name="totalRooms" class="input" required min="1">
                        </div>
                        <div>
                            <label for="room-occupancy" class="block text-sm font-medium text-gray-700 mb-2">Max Occupancy *</label>
                            <input type="number" id="room-occupancy" name="maxOccupancy" class="input" required min="1">
                        </div>
                        <div>
                            <label for="room-beds" class="block text-sm font-medium text-gray-700 mb-2">Bed Count *</label>
                            <input type="number" id="room-beds" name="bedCount" class="input" required min="1">
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="room-bed-type" class="block text-sm font-medium text-gray-700 mb-2">Bed Type</label>
                            <select id="room-bed-type" name="bedType" class="input">
                                <option value="Single">Single</option>
                                <option value="Double">Double</option>
                                <option value="Queen" selected>Queen</option>
                                <option value="King">King</option>
                                <option value="Sofa Bed">Sofa Bed</option>
                            </select>
                        </div>
                        <div>
                            <label for="room-size" class="block text-sm font-medium text-gray-700 mb-2">Room Size</label>
                            <input type="number" id="room-size" name="roomSize" class="input" placeholder="25">
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="room-view" class="block text-sm font-medium text-gray-700 mb-2">View Type</label>
                            <select id="room-view" name="viewType" class="input">
                                <option value="">Select view</option>
                                <option value="City View">City View</option>
                                <option value="Ocean View">Ocean View</option>
                                <option value="Mountain View">Mountain View</option>
                                <option value="Garden View">Garden View</option>
                                <option value="Pool View">Pool View</option>
                                <option value="Interior View">Interior View</option>
                            </select>
                        </div>
                        <div>
                            <label for="room-floor" class="block text-sm font-medium text-gray-700 mb-2">Floor Number</label>
                            <input type="text" id="room-floor" name="floorNumber" class="input">
                        </div>
                    </div>

                    <div>
                        <label for="room-description" class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                        <textarea id="room-description" name="description" rows="3" class="input"></textarea>
                    </div>

                    <div>
                        <label for="room-amenities" class="block text-sm font-medium text-gray-700 mb-2">Room Amenities (comma-separated)</label>
                        <input type="text" id="room-amenities" name="amenities" class="input" placeholder="TV, Mini Bar, Safe, Balcony">
                    </div>

                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div class="flex items-center">
                            <input type="checkbox" id="room-wifi" name="hasWifi" class="mr-2" checked>
                            <label for="room-wifi" class="text-sm text-gray-700">WiFi</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="room-ac" name="hasAirConditioning" class="mr-2" checked>
                            <label for="room-ac" class="text-sm text-gray-700">Air Conditioning</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="room-balcony" name="hasBalcony" class="mr-2">
                            <label for="room-balcony" class="text-sm text-gray-700">Balcony</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="room-kitchen" name="hasKitchen" class="mr-2">
                            <label for="room-kitchen" class="text-sm text-gray-700">Kitchen</label>
                        </div>
                    </div>

                    <div class="flex justify-end space-x-4 pt-4">
                        <button type="button" onclick="closeModal('add-room-modal')" class="btn btn-secondary">Cancel</button>
                        <button type="submit" class="btn btn-primary">Add Room</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/api.js"></script>
    <script src="js/utils.js"></script>
    <script>
        // Hotel Owner Dashboard JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            // Check authentication
            if (!utils.isLoggedIn()) {
                window.location.href = 'login.html';
                return;
            }

            // Load user info
            const user = utils.getCurrentUser();
            if (user && user.firstName) {
                document.getElementById('user-name').textContent = user.firstName;
            }

            // Navigation handling
            const navLinks = document.querySelectorAll('.nav-link');
            const sections = document.querySelectorAll('.dashboard-section');

            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Remove active class from all links
                    navLinks.forEach(l => l.classList.remove('active'));
                    // Add active class to clicked link
                    this.classList.add('active');

                    // Hide all sections
                    sections.forEach(s => s.classList.add('hidden'));
                    // Show target section
                    const targetSection = this.getAttribute('data-section') + '-section';
                    document.getElementById(targetSection).classList.remove('hidden');
                });
            });

            // Load dashboard data
            loadDashboardData();

            // Form submission handlers
            document.getElementById('add-hotel-form').addEventListener('submit', handleHotelSubmit);
            document.getElementById('add-room-form').addEventListener('submit', handleRoomSubmit);
        });

        async function loadDashboardData() {
            try {
                // Load hotel owner's data
                console.log('Loading hotel owner dashboard data...');

                // For now, show placeholder data
                document.getElementById('total-hotels').textContent = '0';
                document.getElementById('total-bookings').textContent = '0';
                document.getElementById('monthly-revenue').textContent = '$0';
                document.getElementById('avg-rating').textContent = '0.0';

            } catch (error) {
                console.error('Error loading dashboard data:', error);
                utils.showAlert('Error loading dashboard data', 'error');
            }
        }

        function showAddHotelModal() {
            document.getElementById('add-hotel-modal').classList.remove('hidden');
        }

        function showAddRoomModal() {
            loadHotelsForRoomForm();
            document.getElementById('add-room-modal').classList.remove('hidden');
        }

        function closeModal(modalId) {
            document.getElementById(modalId).classList.add('hidden');
        }

        async function loadHotelsForRoomForm() {
            try {
                const user = utils.getCurrentUser();
                const token = localStorage.getItem('token');

                const response = await fetch('http://localhost:5093/api/hotels/my-hotels', {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();
                const hotelSelect = document.getElementById('room-hotel-select');
                hotelSelect.innerHTML = '<option value="">Select a hotel</option>';

                if (result.success && result.data) {
                    result.data.forEach(hotel => {
                        const option = document.createElement('option');
                        option.value = hotel.id;
                        option.textContent = hotel.name;
                        hotelSelect.appendChild(option);
                    });
                }
            } catch (error) {
                console.error('Error loading hotels:', error);
            }
        }

        async function handleHotelSubmit(event) {
            event.preventDefault();

            const formData = new FormData(event.target);
            const hotelData = {
                name: formData.get('name'),
                address: formData.get('address'),
                city: formData.get('city'),
                state: formData.get('state'),
                country: formData.get('country'),
                zipCode: formData.get('zipCode'),
                description: formData.get('description'),
                phone: formData.get('phone'),
                email: formData.get('email'),
                website: formData.get('website'),
                latitude: parseFloat(formData.get('latitude')) || 0,
                longitude: parseFloat(formData.get('longitude')) || 0,
                checkInTime: formData.get('checkInTime'),
                checkOutTime: formData.get('checkOutTime'),
                cancellationPolicy: formData.get('cancellationPolicy'),
                amenities: formData.get('amenities') ? formData.get('amenities').split(',').map(a => a.trim()) : []
            };

            try {
                const token = localStorage.getItem('token');
                const response = await fetch('http://localhost:5093/api/hotels', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(hotelData)
                });

                const result = await response.json();

                if (result.success) {
                    utils.showAlert('Hotel added successfully!', 'success');
                    closeModal('add-hotel-modal');
                    event.target.reset();
                    loadDashboardData(); // Refresh data
                } else {
                    utils.showAlert(result.message || 'Failed to add hotel', 'error');
                }
            } catch (error) {
                console.error('Error adding hotel:', error);
                utils.showAlert('Error adding hotel. Please try again.', 'error');
            }
        }

        async function handleRoomSubmit(event) {
            event.preventDefault();

            const formData = new FormData(event.target);
            const roomData = {
                hotelId: parseInt(formData.get('hotelId')),
                roomType: parseInt(formData.get('roomType')),
                roomNumber: formData.get('roomNumber'),
                pricePerNight: parseFloat(formData.get('pricePerNight')),
                discountedPrice: formData.get('discountedPrice') ? parseFloat(formData.get('discountedPrice')) : null,
                totalRooms: parseInt(formData.get('totalRooms')),
                maxOccupancy: parseInt(formData.get('maxOccupancy')),
                bedCount: parseInt(formData.get('bedCount')),
                bedType: formData.get('bedType'),
                roomSize: parseFloat(formData.get('roomSize')) || 0,
                roomSizeUnit: 'sqm',
                description: formData.get('description'),
                viewType: formData.get('viewType'),
                floorNumber: formData.get('floorNumber'),
                hasWifi: formData.get('hasWifi') === 'on',
                hasAirConditioning: formData.get('hasAirConditioning') === 'on',
                hasBalcony: formData.get('hasBalcony') === 'on',
                hasKitchen: formData.get('hasKitchen') === 'on',
                amenities: formData.get('amenities') ? formData.get('amenities').split(',').map(a => a.trim()) : []
            };

            try {
                const token = localStorage.getItem('token');
                const response = await fetch('http://localhost:5093/api/rooms', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(roomData)
                });

                const result = await response.json();

                if (result.success) {
                    utils.showAlert('Room added successfully!', 'success');
                    closeModal('add-room-modal');
                    event.target.reset();
                    loadDashboardData(); // Refresh data
                } else {
                    utils.showAlert(result.message || 'Failed to add room', 'error');
                }
            } catch (error) {
                console.error('Error adding room:', error);
                utils.showAlert('Error adding room. Please try again.', 'error');
            }
        }
    </script>

    <style>
        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            color: #6b7280;
            text-decoration: none;
            border-radius: 0.5rem;
            transition: all 0.15s ease-in-out;
        }

        .nav-link:hover {
            background-color: #f3f4f6;
            color: #374151;
        }

        .nav-link.active {
            background-color: #3b82f6;
            color: white;
        }
    </style>
</body>
</html>
