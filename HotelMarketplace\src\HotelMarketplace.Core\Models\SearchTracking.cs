using System;
using System.ComponentModel.DataAnnotations;

namespace HotelMarketplace.Core.Models
{
    public class SearchTracking
    {
        public int Id { get; set; }

        public string? UserId { get; set; }

        [Required]
        [MaxLength(500)]
        public string Query { get; set; } = string.Empty;

        [MaxLength(100)]
        public string? Location { get; set; }

        public DateTime? CheckIn { get; set; }

        public DateTime? CheckOut { get; set; }

        public int Guests { get; set; }

        public int Rooms { get; set; }

        public int ResultCount { get; set; }

        public string? Filters { get; set; } // JSON string of applied filters

        [MaxLength(50)]
        public string? SortBy { get; set; }

        [MaxLength(500)]
        public string? UserAgent { get; set; }

        [MaxLength(45)]
        public string? IpAddress { get; set; }

        [MaxLength(100)]
        public string? SessionId { get; set; }

        public DateTime SearchedAt { get; set; }

        public long ResponseTimeMs { get; set; }

        public bool ResultClicked { get; set; }

        public int? ClickedHotelId { get; set; }

        public DateTime? ClickedAt { get; set; }

        public bool ConvertedToBooking { get; set; }

        public int? BookingId { get; set; }

        // Navigation Properties
        public User? User { get; set; }
        public Hotel? ClickedHotel { get; set; }
        public Booking? Booking { get; set; }
    }
}
