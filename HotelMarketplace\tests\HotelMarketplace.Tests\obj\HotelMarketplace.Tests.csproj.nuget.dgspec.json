{"format": 1, "restore": {"C:\\Projects\\HotelMarketplace\\tests\\HotelMarketplace.Tests\\HotelMarketplace.Tests.csproj": {}}, "projects": {"C:\\Projects\\HotelMarketplace\\src\\HotelMarketplace.Application\\HotelMarketplace.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Projects\\HotelMarketplace\\src\\HotelMarketplace.Application\\HotelMarketplace.Application.csproj", "projectName": "HotelMarketplace.Application", "projectPath": "C:\\Projects\\HotelMarketplace\\src\\HotelMarketplace.Application\\HotelMarketplace.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Projects\\HotelMarketplace\\src\\HotelMarketplace.Application\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Projects\\HotelMarketplace\\src\\HotelMarketplace.Core\\HotelMarketplace.Core.csproj": {"projectPath": "C:\\Projects\\HotelMarketplace\\src\\HotelMarketplace.Core\\HotelMarketplace.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[12.0.1, )"}, "Microsoft.AspNetCore.Identity": {"target": "Package", "version": "[2.3.1, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.IdentityModel.Tokens": {"target": "Package", "version": "[8.9.0, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[8.9.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Projects\\HotelMarketplace\\src\\HotelMarketplace.Core\\HotelMarketplace.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Projects\\HotelMarketplace\\src\\HotelMarketplace.Core\\HotelMarketplace.Core.csproj", "projectName": "HotelMarketplace.Core", "projectPath": "C:\\Projects\\HotelMarketplace\\src\\HotelMarketplace.Core\\HotelMarketplace.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Projects\\HotelMarketplace\\src\\HotelMarketplace.Core\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.AspNetCore.Http.Features": {"target": "Package", "version": "[5.0.17, )"}, "Microsoft.Extensions.Identity.Stores": {"target": "Package", "version": "[9.0.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Projects\\HotelMarketplace\\src\\HotelMarketplace.Infrastructure\\HotelMarketplace.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Projects\\HotelMarketplace\\src\\HotelMarketplace.Infrastructure\\HotelMarketplace.Infrastructure.csproj", "projectName": "HotelMarketplace.Infrastructure", "projectPath": "C:\\Projects\\HotelMarketplace\\src\\HotelMarketplace.Infrastructure\\HotelMarketplace.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Projects\\HotelMarketplace\\src\\HotelMarketplace.Infrastructure\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Projects\\HotelMarketplace\\src\\HotelMarketplace.Application\\HotelMarketplace.Application.csproj": {"projectPath": "C:\\Projects\\HotelMarketplace\\src\\HotelMarketplace.Application\\HotelMarketplace.Application.csproj"}, "C:\\Projects\\HotelMarketplace\\src\\HotelMarketplace.Core\\HotelMarketplace.Core.csproj": {"projectPath": "C:\\Projects\\HotelMarketplace\\src\\HotelMarketplace.Core\\HotelMarketplace.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Projects\\HotelMarketplace\\src\\HotelMarketplace.WebAPI\\HotelMarketplace.WebAPI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Projects\\HotelMarketplace\\src\\HotelMarketplace.WebAPI\\HotelMarketplace.WebAPI.csproj", "projectName": "HotelMarketplace.WebAPI", "projectPath": "C:\\Projects\\HotelMarketplace\\src\\HotelMarketplace.WebAPI\\HotelMarketplace.WebAPI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Projects\\HotelMarketplace\\src\\HotelMarketplace.WebAPI\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Projects\\HotelMarketplace\\src\\HotelMarketplace.Application\\HotelMarketplace.Application.csproj": {"projectPath": "C:\\Projects\\HotelMarketplace\\src\\HotelMarketplace.Application\\HotelMarketplace.Application.csproj"}, "C:\\Projects\\HotelMarketplace\\src\\HotelMarketplace.Core\\HotelMarketplace.Core.csproj": {"projectPath": "C:\\Projects\\HotelMarketplace\\src\\HotelMarketplace.Core\\HotelMarketplace.Core.csproj"}, "C:\\Projects\\HotelMarketplace\\src\\HotelMarketplace.Infrastructure\\HotelMarketplace.Infrastructure.csproj": {"projectPath": "C:\\Projects\\HotelMarketplace\\src\\HotelMarketplace.Infrastructure\\HotelMarketplace.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"AutoMapper.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[12.0.1, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.4, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[9.0.5, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[8.1.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Projects\\HotelMarketplace\\tests\\HotelMarketplace.Tests\\HotelMarketplace.Tests.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Projects\\HotelMarketplace\\tests\\HotelMarketplace.Tests\\HotelMarketplace.Tests.csproj", "projectName": "HotelMarketplace.Tests", "projectPath": "C:\\Projects\\HotelMarketplace\\tests\\HotelMarketplace.Tests\\HotelMarketplace.Tests.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Projects\\HotelMarketplace\\tests\\HotelMarketplace.Tests\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Projects\\HotelMarketplace\\src\\HotelMarketplace.Application\\HotelMarketplace.Application.csproj": {"projectPath": "C:\\Projects\\HotelMarketplace\\src\\HotelMarketplace.Application\\HotelMarketplace.Application.csproj"}, "C:\\Projects\\HotelMarketplace\\src\\HotelMarketplace.Core\\HotelMarketplace.Core.csproj": {"projectPath": "C:\\Projects\\HotelMarketplace\\src\\HotelMarketplace.Core\\HotelMarketplace.Core.csproj"}, "C:\\Projects\\HotelMarketplace\\src\\HotelMarketplace.Infrastructure\\HotelMarketplace.Infrastructure.csproj": {"projectPath": "C:\\Projects\\HotelMarketplace\\src\\HotelMarketplace.Infrastructure\\HotelMarketplace.Infrastructure.csproj"}, "C:\\Projects\\HotelMarketplace\\src\\HotelMarketplace.WebAPI\\HotelMarketplace.WebAPI.csproj": {"projectPath": "C:\\Projects\\HotelMarketplace\\src\\HotelMarketplace.WebAPI\\HotelMarketplace.WebAPI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.12.0, )"}, "coverlet.collector": {"target": "Package", "version": "[6.0.2, )"}, "xunit": {"target": "Package", "version": "[2.9.2, )"}, "xunit.runner.visualstudio": {"target": "Package", "version": "[2.8.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}}}