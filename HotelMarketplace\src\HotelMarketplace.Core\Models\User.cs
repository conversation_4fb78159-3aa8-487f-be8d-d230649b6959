using Microsoft.AspNetCore.Identity;
using System;
using System.Collections.Generic;

namespace HotelMarketplace.Core.Models
{
    public class User : IdentityUser
    {
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public UserRole Role { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }

        // Navigation properties
        public ICollection<Hotel> OwnedHotels { get; set; }
        public ICollection<Booking> Bookings { get; set; }
        public ICollection<Review> Reviews { get; set; }

        public User()
        {
            OwnedHotels = new List<Hotel>();
            Bookings = new List<Booking>();
            Reviews = new List<Review>();
            CreatedAt = DateTime.UtcNow;
            IsActive = true;
        }
    }
}
