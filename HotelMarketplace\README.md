# Hotel Marketplace System

A comprehensive hotel booking platform built with C# .NET Core backend and modern frontend technologies.

## 🚀 Live Demo

The frontend is currently running with mock data and can be accessed at:
- **Frontend Server**: http://localhost:3000
- **Demo Page**: http://localhost:3000/demo.html

### Demo Credentials

**Admin Access:**
- Email: `<EMAIL>`
- Password: `admin123`

**Customer Access:**
- Email: `<EMAIL>`
- Password: `user123`

## 🏗️ Architecture

### Backend (.NET Core)
- **Clean Architecture** with separation of concerns
- **Entity Framework Core** with SQLite database
- **JWT Authentication** for secure access
- **RESTful API** design
- **Role-based authorization** (Admin, HotelOwner, Customer)

### Frontend (HTML/CSS/JavaScript)
- **Responsive design** with Tailwind CSS
- **Modern JavaScript** with ES6+ features
- **Mock data integration** for development
- **Component-based architecture**

## 📁 Project Structure

```
HotelMarketplace/
├── src/
│   ├── HotelMarketplace.Core/          # Domain models and interfaces
│   ├── HotelMarketplace.Infrastructure/ # Data access and external services
│   ├── HotelMarketplace.Application/   # Business logic and services
│   ├── HotelMarketplace.WebAPI/        # API controllers and configuration
│   └── HotelMarketplace.WebUI/         # Frontend files
│       ├── css/                        # Tailwind CSS styles
│       ├── js/                         # JavaScript modules
│       ├── images/                     # Static images
│       └── *.html                      # HTML pages
└── README.md
```

## 🎯 Features

### ✅ Implemented Features

#### Frontend (Fully Functional with Mock Data)
- **🏠 Homepage** - Hero section with search functionality
- **🔍 Hotel Search** - Advanced search with filters and sorting
- **🏨 Hotel Details** - Comprehensive hotel information pages
- **📅 Booking System** - Complete booking flow with confirmation
- **👤 Authentication** - Login/register with role-based access
- **⚡ Admin Dashboard** - Analytics, user management, hotel approvals
- **📱 Responsive Design** - Mobile-first approach
- **🎨 Modern UI** - Clean design with Tailwind CSS

#### Backend (Partial Implementation)
- **🏗️ Clean Architecture** - Well-structured codebase
- **📊 Database Models** - Complete entity relationships
- **🔐 Authentication** - JWT token-based auth
- **📡 API Controllers** - RESTful endpoints
- **🗄️ Repository Pattern** - Data access abstraction

### 🚧 In Progress
- **Backend Services** - Business logic implementation
- **Database Integration** - Entity Framework setup
- **API Testing** - Endpoint validation

## 🛠️ Technologies Used

### Backend
- **C# .NET Core 6.0**
- **Entity Framework Core**
- **SQLite Database**
- **JWT Authentication**
- **AutoMapper**
- **FluentValidation**

### Frontend
- **HTML5 & CSS3**
- **JavaScript (ES6+)**
- **Tailwind CSS**
- **Font Awesome Icons**
- **Chart.js** (for admin dashboard)

## 🚀 Getting Started

### Prerequisites
- .NET 6.0 SDK
- Node.js (for frontend development)
- Git

### Running the Frontend (Current Demo)

1. **Clone the repository:**
   ```bash
   git clone https://github.com/Hosseinglm/HotelMarketplace.git
   cd HotelMarketplace
   ```

2. **Start the frontend server:**
   ```bash
   cd src/HotelMarketplace.WebUI
   npx http-server -p 3000
   ```

3. **Open your browser:**
   - Main site: http://localhost:3000
   - Demo page: http://localhost:3000/demo.html

### Running the Backend (When Ready)

1. **Restore packages:**
   ```bash
   dotnet restore
   ```

2. **Update database:**
   ```bash
   dotnet ef database update --project src/HotelMarketplace.Infrastructure
   ```

3. **Run the API:**
   ```bash
   dotnet run --project src/HotelMarketplace.WebAPI
   ```

## 📱 Pages Overview

### Public Pages
- **Homepage** (`index.html`) - Landing page with hero section
- **Hotels** (`hotels.html`) - Hotel search and listing
- **Hotel Details** (`hotel-details.html`) - Individual hotel information
- **Login/Register** (`login.html`, `register.html`) - Authentication

### User Pages
- **Booking Confirmation** (`booking-confirmation.html`) - Booking details
- **My Bookings** - User booking history (planned)
- **Profile** - User profile management (planned)

### Admin Pages
- **Admin Dashboard** (`admin-dashboard.html`) - Analytics and management
- **User Management** - Admin user controls
- **Hotel Management** - Hotel approval system

## 🎨 Design Features

- **Modern UI/UX** - Clean, professional design
- **Responsive Layout** - Works on all devices
- **Interactive Elements** - Hover effects, smooth transitions
- **Accessibility** - ARIA labels, keyboard navigation
- **Performance** - Optimized images and code

## 🔧 Configuration

### Mock Data Mode
Currently running in mock data mode. To switch to live API:

1. Edit `src/HotelMarketplace.WebUI/js/api.js`
2. Set `USE_MOCK_DATA = false`
3. Ensure backend API is running

### Environment Variables
- `API_BASE_URL` - Backend API endpoint
- `JWT_SECRET` - JWT signing key
- `DATABASE_URL` - Database connection string

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 👨‍💻 Author

**Hossein Gholami**
- GitHub: [@Hosseinglm](https://github.com/Hosseinglm)

## 🙏 Acknowledgments

- Tailwind CSS for the amazing utility-first framework
- Font Awesome for the beautiful icons
- Unsplash for the high-quality images
- Chart.js for the interactive charts

---

**Note**: This project is currently in development. The frontend is fully functional with mock data, while the backend implementation is in progress.
